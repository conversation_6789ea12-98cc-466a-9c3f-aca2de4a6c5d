<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Options')); ?> - <?php echo e($pricing_plan->name); ?></h1>
    <div>
        <a href="<?php echo e(route('admin_pricing_plan_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-backward"></i> <?php echo e(__('Back to Pricing Plans')); ?></a>
        <a href="" class="d-none d-sm-inline-block btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#option_add_modal"><i class="fas fa-plus"></i> <?php echo e(__('Add Option')); ?></a>
        
        
        <!-- Modal -->
        <div class="modal fade" id="option_add_modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel"><?php echo e(__('Add Option')); ?></h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="<?php echo e(route('admin_pricing_plan_option_store',$pricing_plan->id)); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Name')); ?> *</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary btn-sm"><?php echo e(__('Submit')); ?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- // Modal -->


    </div>
    </a>
</div>


<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-sm" id="dtable">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Name')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i=0; ?>
                    <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $i++; ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td><?php echo e($item->name); ?></td>
                        <td>
                            <a href="" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#option_edit_modal_<?php echo e($i); ?>"><i class="fas fa-edit"></i></a>
                            <a href="<?php echo e(route('admin_pricing_plan_option_destroy',$item->id)); ?>" class="btn btn-danger btn-sm" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><i class="fas fa-trash"></i></a>
                        </td>
                    </tr>
                    <!-- Modal -->
                    <div class="modal fade" id="option_edit_modal_<?php echo e($i); ?>" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5" id="exampleModalLabel"><?php echo e(__('Edit Option')); ?></h1>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form action="<?php echo e(route('admin_pricing_plan_option_update',$item->id)); ?>" method="post">
                                        <?php echo csrf_field(); ?>
                                        <div class="mb-3">
                                            <label for="" class="form-label"><?php echo e(__('Name')); ?> *</label>
                                            <input type="text" name="name" class="form-control" value="<?php echo e($item->name); ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <button type="submit" class="btn btn-primary btn-sm"><?php echo e(__('Update')); ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- // Modal -->
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/pricing_plan/option.blade.php ENDPATH**/ ?>