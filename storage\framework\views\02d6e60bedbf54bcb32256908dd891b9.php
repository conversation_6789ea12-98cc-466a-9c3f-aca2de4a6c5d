<?php $__env->startSection('seo_title', $global_other_page_items->page_faq_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_faq_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_faq_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_faq_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="">
    <div class="container">
        <div class="row">
            <div class="col">
                <ul class="accordion-box wow fadeInRight">
                    <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="accordion block <?php if($loop->iteration == 1): ?> active-block <?php endif; ?>">
                        <div class="acc-btn <?php if($loop->iteration == 1): ?> active <?php endif; ?>"><?php echo e($faq->question); ?>

                            <div class="icon fa fa-plus"></div>
                        </div>
                        <div class="acc-content <?php if($loop->iteration == 1): ?> current <?php endif; ?>">
                            <div class="content">
                                <div class="text">
                                    <?php echo clean($faq->answer); ?>

                                </div>
                            </div>
                        </div>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/front/faqs.blade.php ENDPATH**/ ?>