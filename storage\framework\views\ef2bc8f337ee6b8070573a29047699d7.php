<?php $__env->startSection('seo_title', $global_other_page_items->page_search_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_search_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e(__('Search By:')); ?> <?php echo e($search_by); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><a href="<?php echo e(route('blog')); ?>"><?php echo e($global_other_page_items->page_blog_title); ?></a></li>
                <li><?php echo e($search_by); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="news-section">
    <div class="auto-container">
        <div class="row">
            <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="news-block col-lg-4 col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner-box">
                        <div class="image-box">
                            <figure class="image"><a href="<?php echo e(route('post',$item->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></a></figure>
                        </div>
                        <div class="content-box">
                            <span class="date">
                                <?php echo e($item->created_at->format('d M, Y')); ?>

                            </span>
                            <ul class="post-info">
                                <li><i class="fa fa-user-circle"></i> <?php echo e(__('by Admin')); ?></li>
                            </ul>
                            <h4 class="title"><a href="<?php echo e(route('post',$item->slug)); ?>"><?php echo e($item->title); ?></a></h4>
                            <a href="<?php echo e(route('post',$item->slug)); ?>" class="read-more"><?php echo e(__('Read More')); ?> <i class="fa fa-long-arrow-alt-right"></i></a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-danger text-center"><?php echo e(__('No Post Found')); ?></div>
            <?php endif; ?>
            <div class="col-md-12">
                <?php echo e($posts->links()); ?>

            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/front/search.blade.php ENDPATH**/ ?>