<?php $__env->startSection('seo_title', $global_other_page_items->page_contact_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_contact_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_contact_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_contact_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="contact-details">
    <div class="container ">
        <div class="row">
            <div class="col-xl-7 col-lg-6">
                <div class="sec-title">
                    <span class="sub-title"><?php echo e($global_other_page_items->page_contact_send_mail_subheading); ?></span>
                    <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_contact_send_mail_heading))); ?></h2>
                </div>
                <form id="contact_form" name="contact_form" class="" action="<?php echo e(route('contact_send_message')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <input name="name" class="form-control" type="text" placeholder="<?php echo e(__('Full Name')); ?>" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <input name="email" class="form-control" type="email" placeholder="<?php echo e(__('Email Address')); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <input name="subject" class="form-control" type="text" placeholder="<?php echo e(__('Subject')); ?>" required>
                    </div>
                    <div class="mb-3">
                        <textarea name="message" class="form-control" rows="7" placeholder="<?php echo e(__('Message')); ?>" required></textarea>
                    </div>
                    
                    <?php if($global_setting->google_recaptcha_status == 'Show'): ?>
                    <div class="mb-3">
                        <div class="g-recaptcha" data-sitekey="<?php echo e($global_setting->google_recaptcha_site_key); ?>"></div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <input name="form_botcheck" class="form-control" type="hidden" value="" />
                        <button type="submit" class="theme-btn btn-style-one" data-loading-text="<?php echo e(__('Please wait...')); ?>"><span class="btn-title"><?php echo e(__('Send Message')); ?></span></button>
                    </div>
                </form>
            </div>
            <div class="col-xl-5 col-lg-6">
                <div class="contact-details__right">
                    <div class="sec-title">
                        <span class="sub-title"><?php echo e($global_other_page_items->page_contact_info_subheading); ?></span>
                        <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_contact_info_heading))); ?></h2>
                        <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_contact_info_text))); ?></div>
                    </div>
                    <ul class="list-unstyled contact-details__info">
                        <li>
                            <div class="icon bg-theme-color2">
                                <span class="lnr-icon-phone-plus"></span>
                            </div>
                            <div class="text">
                                <h6><?php echo e($global_other_page_items->page_contact_info_phone_title); ?></h6>
                                <a href="tel:<?php echo e($global_other_page_items->page_contact_info_phone_value); ?>"><?php echo e($global_other_page_items->page_contact_info_phone_value); ?></a>
                            </div>
                        </li>
                        <li>
                            <div class="icon">
                                <span class="lnr-icon-envelope1"></span>
                            </div>
                            <div class="text">
                                <h6><?php echo e($global_other_page_items->page_contact_info_email_title); ?></h6>
                                <a href="mailto:<?php echo e($global_other_page_items->page_contact_info_email_value); ?>"><?php echo e($global_other_page_items->page_contact_info_email_value); ?></a>
                            </div>
                        </li>
                        <li>
                            <div class="icon">
                                <span class="lnr-icon-location"></span>
                            </div>
                            <div class="text">
                                <h6><?php echo e($global_other_page_items->page_contact_info_address_title); ?></h6>
                                <span><?php echo e($global_other_page_items->page_contact_info_address_value); ?></span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<?php if($global_setting->map != ''): ?>
<section class="map-section">
    <?php echo $global_setting->map; ?>

</section>
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/front/contact.blade.php ENDPATH**/ ?>