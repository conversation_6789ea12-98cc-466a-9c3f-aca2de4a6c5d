<?php $__env->startSection('seo_title', $global_other_page_items->page_services_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_services_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_services_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_services_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="services-section">
    <div class="auto-container">
        <div class="row">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="service-block col-lg-3 col-md-6 coll-md-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><img src="<?php echo e(asset('uploads/'.$service->photo)); ?>" alt="<?php echo e($service->name); ?>"></figure>
                    </div>
                    <div class="content-box">
                        <i class="icon <?php echo e($service->icon); ?>"></i>
                        <h5 class="title"><?php echo e($service->name); ?></h5>
                    </div>
                    <div class="hover-content">
                        <i class="icon <?php echo e($service->icon); ?>"></i>
                        <h5 class="title"><a href="<?php echo e(route('service',$service->slug)); ?>"><?php echo e($service->name); ?></a></h5>
                        <div class="text">
                            <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($service->short_description))); ?>

                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/services.blade.php ENDPATH**/ ?>