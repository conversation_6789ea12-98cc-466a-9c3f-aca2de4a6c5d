<script src="<?php echo e(asset('dist-front/js/jquery-3.7.1.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/popper.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/bootstrap.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/jquery.fancybox.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/jquery-ui.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/jquery.countdown.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/bxslider.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/mixitup.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/wow.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/appear.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/select2.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/swiper.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/sweetalert2.min.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/owl.js')); ?>"></script>
<script src="<?php echo e(asset('dist-front/js/script.js')); ?>"></script>

<?php if($global_setting->google_recaptcha_status == 'Show'): ?>
<script src='https://www.google.com/recaptcha/api.js'></script>
<?php endif; ?>

<?php if($errors->any()): ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });
        Toast.fire({
            icon: 'error',
            title: '<?php echo e($error); ?>'
        });
    </script>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php if(Session::has('error')): ?>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });
        Toast.fire({
            icon: 'error',
            title: '<?php echo e(Session::get("error")); ?>'
        });
    </script>
<?php endif; ?>
<?php if(Session::has('success')): ?>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });
        Toast.fire({
            icon: "success",
            title: "<?php echo e(Session::get("success")); ?>"
        });
    </script>
<?php endif; ?>
<?php if(Session::has('info')): ?>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 1800,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });
        Toast.fire({
            icon: "info",
            title: "<?php echo e(Session::get("info")); ?>"
        });
    </script>
<?php endif; ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/layouts/scripts.blade.php ENDPATH**/ ?>