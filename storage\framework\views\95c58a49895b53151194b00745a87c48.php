<?php $__env->startSection('seo_title', $post->seo_title); ?>
<?php $__env->startSection('seo_meta_description', $post->seo_meta_description); ?>

<?php $__env->startSection('content'); ?>

<script type='text/javascript' src='https://platform-api.sharethis.com/js/sharethis.js#property=5993ef01e2587a001253a261&product=inline-share-buttons' async='async'></script>

<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($post->title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><a href="<?php echo e(route('blog')); ?>"><?php echo e($global_other_page_items->page_blog_title); ?></a></li>
                <li><?php echo e($post->title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="blog-details">
    <div class="container">
        <div class="row">
            <div class="col-xl-8 col-lg-7">
                <div class="blog-details__left">
                    <div class="blog-details__img">
                        <img src="<?php echo e(asset('uploads/'.$post->photo)); ?>" alt="">
                        <div class="blog-details__date">
                            <span class="day">
                                <?php echo e($post->created_at->format('d')); ?>

                            </span>
                            <span class="month">
                                <?php echo e($post->created_at->format('M')); ?>

                            </span>
                        </div>
                    </div>
                    <div class="blog-details__content">
                        <ul class="list-unstyled blog-details__meta">
                            <li><a href="javascript:void;"><i class="fas fa-user-circle"></i> <?php echo e(__('Admin')); ?></a></li>
                        </ul>
                        <p class="blog-details__text-2">
                            <?php echo clean($post->description); ?>

                        </p>
                    </div>
                    <div class="blog-details__bottom">
                        <?php if(count($post_tags) != 0): ?>
                        <p class="blog-details__tags"> <span><?php echo e(__('Tags')); ?></span> 
                            <?php for($i=0;$i<count($post_tags);$i++): ?>
                            <a href="<?php echo e(route('tag',$post_tags[$i])); ?>"><?php echo e($post_tags[$i]); ?></a>
                            <?php endfor; ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-lg-5">
                <div class="sidebar">
                    <div class="sidebar__single sidebar__search">
                        <form action="<?php echo e(route('search')); ?>" class="sidebar__search-form" method="get">
                            <input name="search_text" type="search" placeholder="Search here" required>
                            <button type="submit"><i class="lnr-icon-search"></i></button>
                        </form>
                    </div>
                    <div class="sidebar__single sidebar__post">
                        <h3 class="sidebar__title"><?php echo e(__('Latest Posts')); ?></h3>
                        <ul class="sidebar__post-list list-unstyled">
                            <?php $__currentLoopData = $latest_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <div class="sidebar__post-image"> <img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""> </div>
                                <div class="sidebar__post-content">
                                    <h3> <span class="sidebar__post-content-meta"><i
                                        class="fas fa-user-circle"></i><?php echo e(__('Admin')); ?></span> <a href="<?php echo e(route('post',$item->slug)); ?>"><?php echo e($item->title); ?></a>
                                    </h3>
                                </div>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                    <div class="sidebar__single sidebar__category">
                        <h3 class="sidebar__title mb-20"><?php echo e(__('Categories')); ?></h3>
                        <ul class="sidebar__category-list list-unstyled">
                            <?php $__currentLoopData = $post_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(route('category', $item->slug)); ?>"><?php echo e($item->name); ?><span
                                class="icon-right-arrow"></span></a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>

                    <?php if(count($tags) != 0): ?>
                    <div class="sidebar__single sidebar__tags">
                        <h3 class="sidebar__title"><?php echo e(__('Tags')); ?></h3>
                        <div class="sidebar__tags-list">
                            <?php for($i=0;$i<count($tags);$i++): ?>
                            <a href="<?php echo e(route('tag', $tags[$i])); ?>"><?php echo e($tags[$i]); ?></a>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/front/post.blade.php ENDPATH**/ ?>