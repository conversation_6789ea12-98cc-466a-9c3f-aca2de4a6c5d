<?php $__env->startSection('seo_title', $global_other_page_items->page_about_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_about_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<!-- Start main-content -->
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_about_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_about_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<!-- end main-content -->

<?php if($global_other_page_items->page_about_welcome_status == 'Show'): ?>
<section class="about-section">
    <div class="auto-container">
        <div class="row">
            <div class="content-column col-lg-6 col-md-12 col-sm-12 order-2 wow fadeInRight">
                <div class="inner-column">
                    <div class="sec-title">
                        <span class="sub-title"><?php echo e($welcome_one_items->subheading); ?></span>
                        <h2><?php echo e($welcome_one_items->heading); ?></h2>
                        <div class="text">
                            <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($welcome_one_items->text))); ?>

                        </div>
                    </div>
                    <?php $__currentLoopData = $welcome_one_item_elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="info-box">
                        <div class="inner">
                            <i class="icon <?php echo e($item->icon); ?>"></i>
                            <h5 class="title"><?php echo e($item->heading); ?></h5>
                            <div class="text">
                                <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($item->text))); ?>

                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <div class="other-info">
                        <div class="author-info">
                            <div class="inner">
                                <figure class="thumb"><img src="<?php echo e(asset('uploads/'.$welcome_one_items->person_photo)); ?>" alt=""></figure>
                                <h5 class="name"><?php echo e($welcome_one_items->person_name); ?></h5>
                                <span class="designation"><?php echo e($welcome_one_items->person_designation); ?></span>
                            </div>
                        </div>
                        <a href="<?php echo e($welcome_one_items->button_url); ?>" class="theme-btn btn-style-one"><span class="btn-title"><?php echo e($welcome_one_items->button_text); ?></span></a>
                    </div>
                </div>
            </div>

            <div class="image-column col-lg-6 col-md-12 col-sm-12 wow fadeInLeft">
                <div class="image-box">
                    <span class="icon-dots bounce-y"></span>
                    <span class="icon-circle zoom-one"></span>
                    <figure class="image-1 wow fadeIn"><img src="<?php echo e(asset('uploads/'.$welcome_one_items->photo1)); ?>" alt=""></figure>
                    <figure class="image-2 wow fadeIn" data-wow-delay="600ms"><img src="<?php echo e(asset('uploads/'.$welcome_one_items->photo2)); ?>" alt=""></figure>
                    <div class="exp-box">
                        <div class="inner">
                            <i class="icon flaticon-promotion"></i>
                            <span class="count"><?php echo e($welcome_one_items->experience_year); ?></span>
                            <div class="text"><?php echo e(__('Work Experience')); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($global_other_page_items->page_about_service_status == 'Show'): ?>
<section class="services-section">
    <div class="bg-shape"></div>
    <div class="bg bg-pattern-1"></div>
    <div class="auto-container">
        <div class="sec-title light">
            <div class="row">
                <div class="col-lg-7">
                    <span class="sub-title"><?php echo e($global_other_page_items->page_about_service_subheading); ?></span>
                    <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_service_heading))); ?></h2>
                </div>
                <div class="col-lg-5">
                    <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_service_text))); ?></div>
                </div>
            </div>
        </div>
        <div class="row">
            <?php $__currentLoopData = $services->take($global_other_page_items->page_about_service_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="service-block col-lg-3 col-md-6 coll-md-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><img src="<?php echo e(asset('uploads/'.$service->photo)); ?>" alt="<?php echo e($service->name); ?>"></figure>
                    </div>
                    <div class="content-box">
                        <i class="icon <?php echo e($service->icon); ?>"></i>
                        <h5 class="title"><?php echo e($service->name); ?></h5>
                    </div>
                    <div class="hover-content">
                        <i class="icon <?php echo e($service->icon); ?>"></i>
                        <h5 class="title"><a href="<?php echo e(route('service',$service->slug)); ?>"><?php echo e($service->name); ?></a></h5>
                        <div class="text"><?php echo e($service->short_description); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($global_other_page_items->page_about_team_members_status == 'Show'): ?>
<section class="team-section pt-10">    
    <div class="auto-container">
        <div class="sec-title text-center">
            <span class="sub-title"><?php echo e($global_other_page_items->page_about_team_members_subheading); ?></span>
            <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_other_page_items->page_about_team_members_heading))); ?></h2>
        </div>
        <div class="row">
            <?php $__currentLoopData = $team_members->take($global_other_page_items->page_about_team_members_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="team-block-two col-lg-4 col-md-6 col-sm-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="info-box">
                        <h4 class="name"><a href="<?php echo e(route('team_member',$item->slug)); ?>"><?php echo e($item->name); ?></a></h4>
                        <span class="designation"><?php echo e($item->designation); ?></span>
                    </div>
                    <div class="image-box">
                        <figure class="image"><a href="<?php echo e(route('team_member',$item->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></a></figure> 
                        <div class="social-links">
                            <?php if($item->facebook != ''): ?>
                                <a href="<?php echo e($item->facebook); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if($item->twitter != ''): ?>
                                <a href="<?php echo e($item->twitter); ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                            <?php endif; ?>
                            <?php if($item->linkedin != ''): ?>
                                <a href="<?php echo e($item->linkedin); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                            <?php endif; ?>
                            <?php if($item->instagram != ''): ?>
                                <a href="<?php echo e($item->instagram); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                            <?php if($item->youtube != ''): ?>
                                <a href="<?php echo e($item->youtube); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                            <?php endif; ?>
                            <?php if($item->pinterest != ''): ?>
                                <a href="<?php echo e($item->pinterest); ?>" target="_blank"><i class="fab fa-pinterest-p"></i></a>
                            <?php endif; ?>
                        </div>
                        <span class="share-icon fas fa-plus"></span>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.pronixs.com/resources/views/front/about.blade.php ENDPATH**/ ?>