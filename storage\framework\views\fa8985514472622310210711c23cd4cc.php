<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit Portfolio')); ?></h1>
    <a href="<?php echo e(route('admin_portfolio_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_portfolio_update',$portfolio->id)); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Name')); ?> *</label>
                        <input type="text" name="name" class="form-control" value="<?php echo e($portfolio->name); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Slug')); ?>*</label>
                        <input type="text" name="slug" class="form-control" value="<?php echo e($portfolio->slug); ?>">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Description')); ?> * </label>
                <textarea name="description" class="form-control editor" cols="30" rows="10"><?php echo e($portfolio->description); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Date')); ?></label>
                        <input type="text" name="date" class="form-control" value="<?php echo e($portfolio->date); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Client')); ?></label>
                        <input type="text" name="client" class="form-control" value="<?php echo e($portfolio->client); ?>">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Website')); ?></label>
                        <input type="text" name="website" class="form-control" value="<?php echo e($portfolio->website); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Location')); ?></label>
                        <input type="text" name="location" class="form-control" value="<?php echo e($portfolio->location); ?>">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Title')); ?></label>
                <input type="text" name="seo_title" class="form-control" value="<?php echo e($portfolio->seo_title); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Meta Description')); ?></label>
                <textarea name="seo_meta_description" class="form-control h_100" cols="30" rows="10"><?php echo e($portfolio->seo_meta_description); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                        <div class="photo-container">
                            <a href="<?php echo e(asset('uploads/'.$portfolio->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$portfolio->photo)); ?>" alt=""></a>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                        <div><input type="file" name="photo"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Banner')); ?></label>
                        <div class="photo-container">
                            <a href="<?php echo e(asset('uploads/'.$portfolio->banner)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$portfolio->banner)); ?>" alt=""></a>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Banner')); ?></label>
                        <div><input type="file" name="banner"></div>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/portfolio/edit.blade.php ENDPATH**/ ?>