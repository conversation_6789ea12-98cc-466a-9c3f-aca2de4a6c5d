<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Settings')); ?></h1>
</div>

<form action="<?php echo e(route('admin_setting_general_update')); ?>" method="post" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <div class="card shadow mb-4 t-left">
        <div class="card-body">
            <div class="row">
                <div class="col-xl-3 col-lg-4 col-md-5 col-sm-12">
                    <ul class="nav flex-column nav-tabs" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" id="tab__logo_favicon" data-bs-toggle="pill" href="#item__logo_favicon" role="tab" aria-controls="item__logo_favicon" aria-selected="true"><?php echo e(__('Logo and Favicon')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__404" data-bs-toggle="pill" href="#item__404" role="tab" aria-controls="item__404" aria-selected="false"><?php echo e(__('404 Page Photo')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__banner" data-bs-toggle="pill" href="#item__banner" role="tab" aria-controls="item__banner" aria-selected="false"><?php echo e(__('Banner')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__login_page_photo" data-bs-toggle="pill" href="#item__login_page_photo" role="tab" aria-controls="item__login_page_photo" aria-selected="false"><?php echo e(__('Login Page Photo')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__home_page" data-bs-toggle="pill" href="#item__home_page" role="tab" aria-controls="item__home_page" aria-selected="false"><?php echo e(__('Home Page Setup')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__social_media" data-bs-toggle="pill" href="#item__social_media" role="tab" aria-controls="item__social_media" aria-selected="false"><?php echo e(__('Social Media')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__top_bar" data-bs-toggle="pill" href="#item__top_bar" role="tab" aria-controls="item__top_bar" aria-selected="false"><?php echo e(__('Top Bar')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__footer" data-bs-toggle="pill" href="#item__footer" role="tab" aria-controls="item__footer" aria-selected="false"><?php echo e(__('Footer')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__map" data-bs-toggle="pill" href="#item__map" role="tab" aria-controls="item__map" aria-selected="false"><?php echo e(__('Map')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__cookie_consent" data-bs-toggle="pill" href="#item__cookie_consent" role="tab" aria-controls="item__cookie_consent" aria-selected="false"><?php echo e(__('Cookie Consent')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__google_analytic" data-bs-toggle="pill" href="#item__google_analytic" role="tab" aria-controls="item__google_analytic" aria-selected="false"><?php echo e(__('Google Analytic')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__google_recaptcha" data-bs-toggle="pill" href="#item__google_recaptcha" role="tab" aria-controls="item__google_recaptcha" aria-selected="false"><?php echo e(__('Google Recaptcha')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__tawk_live_chat" data-bs-toggle="pill" href="#item__tawk_live_chat" role="tab" aria-controls="item__tawk_live_chat" aria-selected="false"><?php echo e(__('Tawk Live Chat')); ?></a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="tab__other" data-bs-toggle="pill" href="#item__other" role="tab" aria-controls="item__other" aria-selected="false"><?php echo e(__('Other')); ?></a>
                        </li>
                    </ul>
                </div>
                <div class="col-xl-9 col-lg-8 col-md-7 col-sm-12">
                    <div class="tab-content" id="v-pills-tabContent">
                        <div class="tab-pane fade show active" id="item__logo_favicon" role="tabpanel" aria-labelledby="tab__logo_favicon">
                            <table class="table table-bordered">
                                <tr>
                                    <td class="w_50_p">
                                        <?php echo e(__('Existing Logo')); ?>

                                    </td>
                                    <td>
                                        <?php echo e(__('Change Logo')); ?>

                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-center bg_eeeeee">
                                        <img src="<?php echo e(asset('uploads/'.$setting->logo)); ?>" alt="" class="w_200">
                                    </td>
                                    <td>
                                        <input type="file" name="logo">
                                    </td>
                                </tr>
                            </table>

                            <table class="table table-bordered">
                                <tr>
                                    <td class="w_50_p">
                                        <?php echo e(__('Existing Logo - Sticky')); ?>

                                    </td>
                                    <td>
                                        <?php echo e(__('Change Logo - Sticky')); ?>

                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-center bg_eeeeee">
                                        <img src="<?php echo e(asset('uploads/'.$setting->logo_sticky)); ?>" alt="" class="w_200">
                                    </td>
                                    <td>
                                        <input type="file" name="logo_sticky">
                                    </td>
                                </tr>
                            </table>

                            <table class="table table-bordered">
                                <tr>
                                    <td class="w_50_p">
                                        <?php echo e(__('Existing Favicon')); ?>

                                    </td>
                                    <td>
                                        <?php echo e(__('Change Favicon')); ?>

                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-center bg_eeeeee">
                                        <img src="<?php echo e(asset('uploads/'.$global_setting->favicon)); ?>" alt="" class="w_100">
                                    </td>
                                    <td>
                                        <input type="file" name="favicon">
                                    </td>
                                </tr>
                            </table>
                        </div>


                        <div class="tab-pane fade" id="item__404" role="tabpanel" aria-labelledby="tab__404">
                            <div class="row">
                                <div class="col-lg-6 col-md-12">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                                        <div>
                                            <img src="<?php echo e(asset('uploads/'.$setting->image_404)); ?>" alt="" class="w_200">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                                        <div>
                                            <input type="file" name="image_404">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="tab-pane fade" id="item__banner" role="tabpanel" aria-labelledby="tab__banner">
                            <div class="row">
                                <div class="col-lg-6 col-md-12">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                                        <div>
                                            <img src="<?php echo e(asset('uploads/'.$setting->banner)); ?>" alt="" class="w_300">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                                        <div>
                                            <input type="file" name="banner">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="tab-pane fade" id="item__login_page_photo" role="tabpanel" aria-labelledby="tab__login_page_photo">
                            <div class="row">
                                <div class="col-lg-6 col-md-12">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                                        <div>
                                            <img src="<?php echo e(asset('uploads/'.$setting->login_page_photo)); ?>" alt="" class="w_300">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                                        <div>
                                            <input type="file" name="login_page_photo">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="tab-pane fade" id="item__home_page" role="tabpanel" aria-labelledby="tab__home_page">
                            <div class="row">
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Home Page Show')); ?></label>
                                    <select name="home_show" class="form-control select2">
                                        <option value="All" <?php echo e($setting->home_show == 'All' ? 'selected' : ''); ?>><?php echo e(__('All')); ?></option>
                                        <option value="Home 1" <?php echo e($setting->home_show == 'Home 1' ? 'selected' : ''); ?>><?php echo e(__('Home 1')); ?></option>
                                        <option value="Home 2" <?php echo e($setting->home_show == 'Home 2' ? 'selected' : ''); ?>><?php echo e(__('Home 2')); ?></option>
                                        <option value="Home 3" <?php echo e($setting->home_show == 'Home 3' ? 'selected' : ''); ?>><?php echo e(__('Home 3')); ?></option>
                                        <option value="Home 4" <?php echo e($setting->home_show == 'Home 4' ? 'selected' : ''); ?>><?php echo e(__('Home 4')); ?></option>
                                    </select>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('SEO Title')); ?></label>
                                        <input type="text" name="home_seo_title" class="form-control" value="<?php echo e($setting->home_seo_title); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('SEO Meta Description')); ?></label>
                                        <textarea name="home_seo_meta_description" class="form-control h_70" cols="30" rows="10"><?php echo e($setting->home_seo_meta_description); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="tab-pane fade" id="item__social_media" role="tabpanel" aria-labelledby="tab__social_media">
                            <div class="row">
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Facebook')); ?></label>
                                        <input type="text" name="facebook" class="form-control" value="<?php echo e($setting->facebook); ?>">
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Twitter')); ?></label>
                                        <input type="text" name="twitter" class="form-control" value="<?php echo e($setting->twitter); ?>">
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Linkedin')); ?></label>
                                        <input type="text" name="linkedin" class="form-control" value="<?php echo e($setting->linkedin); ?>">
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Instagram')); ?></label>
                                        <input type="text" name="instagram" class="form-control" value="<?php echo e($setting->instagram); ?>">
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('YouTube')); ?></label>
                                        <input type="text" name="youtube" class="form-control" value="<?php echo e($setting->youtube); ?>">
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Pinterest')); ?></label>
                                        <input type="text" name="pinterest" class="form-control" value="<?php echo e($setting->pinterest); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__top_bar" role="tabpanel" aria-labelledby="tab__top_bar">
                            <div class="row">
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Email')); ?></label>
                                        <input type="text" name="top_bar_email" class="form-control" value="<?php echo e($setting->top_bar_email); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Address')); ?></label>
                                        <input type="text" name="top_bar_address" class="form-control" value="<?php echo e($setting->top_bar_address); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Phone')); ?></label>
                                        <input type="text" name="top_bar_phone" class="form-control" value="<?php echo e($setting->top_bar_phone); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="tab-pane fade" id="item__footer" role="tabpanel" aria-labelledby="tab__footer">
                            <div class="row">
                                <div class="col-lg-12 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Email')); ?></label>
                                        <input type="text" name="footer_email" class="form-control" value="<?php echo e($setting->footer_email); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Phone')); ?></label>
                                        <input type="text" name="footer_phone" class="form-control" value="<?php echo e($setting->footer_phone); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Address')); ?></label>
                                        <input type="text" name="footer_address" class="form-control" value="<?php echo e($setting->footer_address); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Copyright Text')); ?></label>
                                        <input type="text" name="footer_copyright" class="form-control" value="<?php echo e($setting->footer_copyright); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Footer Text')); ?></label>
                                        <textarea name="footer_text" class="form-control h_100" cols="30" rows="10"><?php echo e($setting->footer_text); ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Links Heading')); ?></label>
                                        <input type="text" name="footer_links_heading" class="form-control" value="<?php echo e($setting->footer_links_heading); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Subscriber Heading')); ?></label>
                                        <input type="text" name="footer_subscriber_heading" class="form-control" value="<?php echo e($setting->footer_subscriber_heading); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Subscriber Text')); ?></label>
                                        <textarea name="footer_subscriber_text" class="form-control h_100" cols="30" rows="10"><?php echo e($setting->footer_subscriber_text); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__map" role="tabpanel" aria-labelledby="tab__map">
                            <div class="row">
                                <div class="col-lg-12 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('iframe code')); ?></label>
                                        <textarea name="map" class="form-control h_150" cols="30" rows="10"><?php echo e($setting->map); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__cookie_consent" role="tabpanel" aria-labelledby="tab__cookie_consent">
                            <div class="row">
                                <div class="col-lg-12 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Message')); ?></label>
                                        <textarea name="cookie_consent_message" class="form-control h_70" cols="30" rows="10"><?php echo e($setting->cookie_consent_message); ?></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Text Color')); ?></label>
                                                <input type="text" name="cookie_consent_text_color" class="form-control jscolor" value="<?php echo e($setting->cookie_consent_text_color); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Background Color')); ?></label>
                                                <input type="text" name="cookie_consent_bg_color" class="form-control jscolor" value="<?php echo e($setting->cookie_consent_bg_color); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Button Text Color')); ?></label>
                                                <input type="text" name="cookie_consent_button_text_color" class="form-control jscolor" value="<?php echo e($setting->cookie_consent_button_text_color); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Button Background Color')); ?></label>
                                                <input type="text" name="cookie_consent_button_bg_color" class="form-control jscolor" value="<?php echo e($setting->cookie_consent_button_bg_color); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Button Text')); ?></label>
                                                <input type="text" name="cookie_consent_button_text" class="form-control" value="<?php echo e($setting->cookie_consent_button_text); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                                                <select name="cookie_consent_status" class="form-select">
                                                    <option value="Show" <?php echo e($setting->cookie_consent_status == 'Show' ? 'selected' : ''); ?>><?php echo e(__('Show')); ?></option>
                                                    <option value="Hide" <?php echo e($setting->cookie_consent_status == 'Hide' ? 'selected' : ''); ?>><?php echo e(__('Hide')); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__google_analytic" role="tabpanel" aria-labelledby="tab__google_analytic">
                            <div class="row">
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Analytic Code')); ?></label>
                                    <input type="text" name="google_analytic_id" class="form-control" value="<?php echo e($setting->google_analytic_id); ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                                    <select name="google_analytic_status" class="form-select">
                                        <option value="Show" <?php echo e($setting->google_analytic_status == 'Show' ? 'selected' : ''); ?>><?php echo e(__('Show')); ?></option>
                                        <option value="Hide" <?php echo e($setting->google_analytic_status == 'Hide' ? 'selected' : ''); ?>><?php echo e(__('Hide')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__google_recaptcha" role="tabpanel" aria-labelledby="tab__google_recaptcha">
                            <div class="row">
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Recaptcha Site Key')); ?></label>
                                    <input type="text" name="google_recaptcha_site_key" class="form-control" value="<?php echo e($setting->google_recaptcha_site_key); ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                                    <select name="google_recaptcha_status" class="form-select">
                                        <option value="Show" <?php echo e($setting->google_recaptcha_status == 'Show' ? 'selected' : ''); ?>><?php echo e(__('Show')); ?></option>
                                        <option value="Hide" <?php echo e($setting->google_recaptcha_status == 'Hide' ? 'selected' : ''); ?>><?php echo e(__('Hide')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__tawk_live_chat" role="tabpanel" aria-labelledby="tab__tawk_live_chat">
                            <div class="row">
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Property Id')); ?></label>
                                    <input type="text" name="tawk_live_chat_property_id" class="form-control" value="<?php echo e($setting->tawk_live_chat_property_id); ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                                    <select name="tawk_live_chat_status" class="form-select">
                                        <option value="Show" <?php echo e($setting->tawk_live_chat_status == 'Show' ? 'selected' : ''); ?>><?php echo e(__('Show')); ?></option>
                                        <option value="Hide" <?php echo e($setting->tawk_live_chat_status == 'Hide' ? 'selected' : ''); ?>><?php echo e(__('Hide')); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane fade" id="item__other" role="tabpanel" aria-labelledby="tab__other">
                            <div class="row">
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Sticky Header')); ?></label>
                                        <select name="sticky_header" class="form-select">
                                            <option value="Show" <?php if($setting->sticky_header == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                                            <option value="Hide" <?php if($setting->sticky_header == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Preloader')); ?></label>
                                        <select name="preloader" class="form-select">
                                            <option value="Show" <?php if($setting->preloader == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                                            <option value="Hide" <?php if($setting->preloader == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Layout Direction')); ?></label>
                                        <select name="layout_direction" class="form-select">
                                            <option value="LTR" <?php if($setting->layout_direction == 'LTR'): ?> selected <?php endif; ?>><?php echo e(__('LTR')); ?></option>
                                            <option value="RTL" <?php if($setting->layout_direction == 'RTL'): ?> selected <?php endif; ?>><?php echo e(__('RTL')); ?></option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Theme Color')); ?></label>
                                        <input type="text" name="theme_color" class="form-control jscolor" value="<?php echo e($setting->theme_color); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Currency Symbol')); ?></label>
                                        <input type="text" name="currency_symbol" class="form-control" value="<?php echo e($setting->currency_symbol); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
    <button type="submit" class="btn btn-success btn-block mb_50 btn-common"><?php echo e(__('Update')); ?></button>
</form>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/admin/setting/general.blade.php ENDPATH**/ ?>