<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Home One Page Item')); ?></h1>
</div>


<form action="<?php echo e(route('admin_home1_page_item_update')); ?>" method="post">
    <?php echo csrf_field(); ?>

    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Service - on slider')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('How Many')); ?></label>
                <input type="text" name="service_on_slider_how_many" class="form-control" value="<?php echo e($page_data->service_on_slider_how_many); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="service_on_slider_status" class="form-select">
                    <option value="Show" <?php if($page_data->service_on_slider_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->service_on_slider_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Welcome')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="welcome_status" class="form-select">
                    <option value="Show" <?php if($page_data->welcome_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->welcome_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Service')); ?></b></h5>
            <div class="mb-3">
                <label for="service_heading" class="form-label"><?php echo e(__('Heading')); ?></label>
                <textarea name="service_heading" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->service_heading); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                <input type="text" name="service_subheading" class="form-control" value="<?php echo e($page_data->service_subheading); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('How Many')); ?></label>
                <input type="text" name="service_how_many" class="form-control" value="<?php echo e($page_data->service_how_many); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="service_status" class="form-select">
                    <option value="Show" <?php if($page_data->service_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->service_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Video 1')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="video_one_status" class="form-select">
                    <option value="Show" <?php if($page_data->video_one_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->video_one_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Fun Fact')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="fun_fact_status" class="form-select">
                    <option value="Show" <?php if($page_data->fun_fact_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->fun_fact_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Portfolio')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Heading')); ?></label>
                <textarea name="portfolio_heading" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->portfolio_heading); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                <input type="text" name="portfolio_subheading" class="form-control" value="<?php echo e($page_data->portfolio_subheading); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('How Many')); ?></label>
                <input type="text" name="portfolio_how_many" class="form-control" value="<?php echo e($page_data->portfolio_how_many); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="portfolio_status" class="form-select">
                    <option value="Show" <?php if($page_data->portfolio_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->portfolio_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Contact')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Heading')); ?></label>
                <textarea name="contact_heading" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->contact_heading); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                <input type="text" name="contact_subheading" class="form-control" value="<?php echo e($page_data->contact_subheading); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="contact_status" class="form-select">
                    <option value="Show" <?php if($page_data->contact_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->contact_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Blog')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Heading')); ?></label>
                <textarea name="blog_heading" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->blog_heading); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                <input type="text" name="blog_subheading" class="form-control" value="<?php echo e($page_data->blog_subheading); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('How Many')); ?></label>
                <input type="text" name="blog_how_many" class="form-control" value="<?php echo e($page_data->blog_how_many); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="blog_status" class="form-select">
                    <option value="Show" <?php if($page_data->blog_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->blog_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Video 2')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="video_two_status" class="form-select">
                    <option value="Show" <?php if($page_data->video_two_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->video_two_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Feature')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="feature_status" class="form-select">
                    <option value="Show" <?php if($page_data->feature_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->feature_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Testimonial')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Heading')); ?></label>
                <textarea name="testimonial_heading" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->testimonial_heading); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                <input type="text" name="testimonial_subheading" class="form-control" value="<?php echo e($page_data->testimonial_subheading); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Text')); ?></label>
                <textarea name="testimonial_text" class="form-control h_100" cols="30" rows="10"><?php echo e($page_data->testimonial_text); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="testimonial_status" class="form-select">
                    <option value="Show" <?php if($page_data->testimonial_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->testimonial_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Why Choose')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="why_choose_status" class="form-select">
                    <option value="Show" <?php if($page_data->why_choose_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->why_choose_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>


    <div class="card shadow mb-4">
        <div class="card-body">
            <h5 class="border-1"><b><?php echo e(__('Client')); ?></b></h5>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Status')); ?></label>
                <select name="client_status" class="form-select">
                    <option value="Show" <?php if($page_data->client_status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                    <option value="Hide" <?php if($page_data->client_status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                </select>
            </div>
        </div>
    </div>

    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
    
</form>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/admin/home_pages/home1.blade.php ENDPATH**/ ?>