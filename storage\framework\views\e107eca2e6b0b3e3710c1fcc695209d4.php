<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit Pricing Plan')); ?></h1>
    <a href="<?php echo e(route('admin_pricing_plan_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_pricing_plan_update',$pricing_plan->id)); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Name')); ?> *</label>
                        <input type="text" name="name" class="form-control" value="<?php echo e($pricing_plan->name); ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Price')); ?> *</label>
                        <input type="text" name="price" class="form-control" value="<?php echo e($pricing_plan->price); ?>">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Period')); ?> *</label>
                        <input type="text" name="period" class="form-control" value="<?php echo e($pricing_plan->period); ?>">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Button Text')); ?> *</label>
                        <input type="text" name="button_text" class="form-control" value="<?php echo e($pricing_plan->button_text); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Button URL')); ?> *</label>
                        <input type="text" name="button_url" class="form-control" value="<?php echo e($pricing_plan->button_url); ?>">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                        <div class="photo-container">
                            <a href="<?php echo e(asset('uploads/'.$pricing_plan->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$pricing_plan->photo)); ?>" alt=""></a>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                        <div><input type="file" name="photo"></div>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/pricing_plan/edit.blade.php ENDPATH**/ ?>