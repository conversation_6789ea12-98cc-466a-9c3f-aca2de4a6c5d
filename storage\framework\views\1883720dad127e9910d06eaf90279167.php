<?php $__env->startSection('seo_title', $global_other_page_items->page_blog_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_blog_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_blog_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_blog_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="news-section">
    <div class="auto-container">
        <div class="row">
            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="news-block col-lg-4 col-md-6 col-sm-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><a href="<?php echo e(route('post',$item->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></a></figure>
                    </div>
                    <div class="content-box">
                        <span class="date">
                            <?php echo e($item->created_at->format('d M, Y')); ?>

                        </span>
                        <ul class="post-info">
                            <li><i class="fa fa-user-circle"></i> <?php echo e(__('by Admin')); ?></li>
                        </ul>
                        <h4 class="title"><a href="<?php echo e(route('post',$item->slug)); ?>"><?php echo e($item->title); ?></a></h4>
                        <a href="<?php echo e(route('post',$item->slug)); ?>" class="read-more"><?php echo e(__('Read More')); ?> <i class="fa <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa-long-arrow-alt-left <?php else: ?> fa-long-arrow-alt-right <?php endif; ?>"></i></a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-12">
                <?php echo e($posts->links()); ?>

            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/front/blog.blade.php ENDPATH**/ ?>