<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit FAQ')); ?></h1>
    <a href="<?php echo e(route('admin_faq_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_faq_update',$faq->id)); ?>" method="post">
            <?php echo csrf_field(); ?>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Question')); ?>*</label>
                <input type="text" name="question" class="form-control" value="<?php echo e($faq->question); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Answer')); ?>*</label>
                <textarea name="answer" class="form-control editor" cols="30" rows="10"><?php echo e($faq->answer); ?></textarea>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/faq/edit.blade.php ENDPATH**/ ?>