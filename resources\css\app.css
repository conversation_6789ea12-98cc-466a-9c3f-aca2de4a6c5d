@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@600&family=Changa:wght@600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

/* Custom animations */
@keyframes bounce-y {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes zoom-one {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.bounce-y {
  animation: bounce-y 2s infinite;
}

.zoom-one {
  animation: zoom-one 3s infinite;
}

/* TBL Tech Custom Styles */
.font-cairo {
  font-family: 'Cairo', sans-serif;
}

.font-changa {
  font-family: 'Changa', sans-serif;
}

/* Hero section improvements */
.hero-title {
  font-family: 'Cairo', sans-serif;
  font-weight: 800;
  line-height: 1.2;
}

.hero-subtitle {
  font-family: 'Cairo', sans-serif;
  font-weight: 300;
  line-height: 1.6;
}

/* Purple gradient background */
.bg-purple-gradient {
  background: linear-gradient(135deg, #351735 0%, #101545 100%);
}