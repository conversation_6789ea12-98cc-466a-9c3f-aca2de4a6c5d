<!DOCTYPE html>
<html <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> dir="rtl" <?php endif; ?> lang="<?php echo e(session('sess_lang_code')); ?>">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <title ><?php echo $__env->yieldContent('seo_title'); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('seo_meta_description'); ?>">
    
    <link rel="shortcut icon" href="<?php echo e(asset('uploads/'.$global_setting->favicon)); ?>" type="image/x-icon">
    <link rel="icon" href="<?php echo e(asset('uploads/'.$global_setting->favicon)); ?>" type="image/x-icon">

    <?php echo $__env->make('front.layouts.styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldContent('dark_mode'); ?>

    <?php if($global_setting->sticky_header == 'Hide'): ?>
    <style >
        .sticky-header.fixed-header {
            display: none;
        }
    </style>
    <?php endif; ?>

    <?php if($global_setting->tawk_live_chat_status == 'Show'): ?>
		<style >
		.scroll-to-top {
			bottom: 50px!important;
		}
		</style>
    <?php endif; ?>

    <?php if($global_setting->cookie_consent_status == 'Show'): ?>
        <script src="https://cdn.websitepolicies.io/lib/cookieconsent/1.0.3/cookieconsent.min.js" defer></script><script >window.addEventListener("load",function(){window.wpcc.init({"colors":{"popup":{"background":"#<?php echo e($global_setting->cookie_consent_bg_color); ?>","text":"#<?php echo e($global_setting->cookie_consent_text_color); ?>","border":"#b3d0e4"},"button":{"background":"#<?php echo e($global_setting->cookie_consent_button_bg_color); ?>","text":"#<?php echo e($global_setting->cookie_consent_button_text_color); ?>"}},"position":"bottom","padding":"large","margin":"none","content":{"message":"<?php echo e($global_setting->cookie_consent_message); ?>","button":"<?php echo e($global_setting->cookie_consent_button_text); ?>"}})});</script>
    <?php endif; ?>

    <?php if($global_setting->google_analytic_status == 'Show'): ?>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo e($global_setting->google_analytic_id); ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', '<?php echo e($global_setting->google_analytic_id); ?>');
    </script>
    <?php endif; ?>

    <style >
    :root {
        --theme-color1: #<?php echo e($global_setting->theme_color); ?>;
    }
    </style>

</head>

<body>

    <div class="page-wrapper">
        
        <?php if($global_setting->preloader == 'Show'): ?>
        <div class="preloader"></div>
        <?php endif; ?>
        
        <!-- Main Header-->
        <header class="main-header header-style-two">
            
            <?php echo $__env->make('front.layouts.top', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
            <div class="header-lower">
                <!-- Main box -->
                <div class="main-box">
                    <div class="logo-box">
                        <div class="logo"><a href="<?php echo e(route('home')); ?>"><img src="<?php echo e(asset('uploads/'.$global_setting->logo)); ?>" alt="<?php echo e(env('APP_NAME')); ?>"></a></div>
                    </div>

                    <!--Nav Box-->
                    <div class="nav-outer">    
                        <nav class="nav main-menu">
                            <ul class="navigation">

                                <?php if($global_setting->home_show == 'All'): ?>
                                <li class="<?php echo e((Request::is('/')||Route::is('home_1')||Route::is('home_2')||Route::is('home_3')||Route::is('home_4')) ? 'current' : ''); ?> dropdown"><a href="javascript:;"><?php echo e(__('Home')); ?></a>
                                    <ul >
                                        <li ><a href="<?php echo e(route('home_1')); ?>"><?php echo e(__('Home Layout 1')); ?></a></li>
                                        <li ><a href="<?php echo e(route('home_2')); ?>"><?php echo e(__('Home Layout 2')); ?></a></li>
                                        <li ><a href="<?php echo e(route('home_3')); ?>"><?php echo e(__('Home Layout 3')); ?></a></li>
                                        <li ><a href="<?php echo e(route('home_4')); ?>"><?php echo e(__('Home Layout 4')); ?></a></li>
                                    </ul>
                                </li>
                                <?php endif; ?>

                                <?php if($global_setting->home_show == 'Home 1'): ?>
                                <li class="<?php echo e(Route::is('home_1') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('home_1')); ?>"><?php echo e(__('Home')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($global_setting->home_show == 'Home 2'): ?>
                                <li class="<?php echo e(Route::is('home_2') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('home_2')); ?>"><?php echo e(__('Home')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($global_setting->home_show == 'Home 3'): ?>
                                <li class="<?php echo e(Route::is('home_3') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('home_3')); ?>"><?php echo e(__('Home')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($global_setting->home_show == 'Home 4'): ?>
                                <li class="<?php echo e(Route::is('home_4') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('home_4')); ?>"><?php echo e(__('Home')); ?></a>
                                </li>
                                <?php endif; ?>


                                <?php $__currentLoopData = $global_menu; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php 
                                    $menu_arr[$item->name] = $item->status;
                                ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <li class="dropdown"><a href="javascript:;"><?php echo e(__('Pages')); ?></a>
                                    <ul >
                                        <?php if($menu_arr['About'] == 'Show'): ?>
                                        <li ><a href="<?php echo e(route('about')); ?>"><?php echo e(__('About Us')); ?></a></li>
                                        <?php endif; ?>

                                        <?php if($menu_arr['Team Members'] == 'Show'): ?>
                                        <li ><a href="<?php echo e(route('team_members')); ?>"><?php echo e(__('Team')); ?></a></li>
                                        <?php endif; ?>

                                        <?php if($menu_arr['Testimonials'] == 'Show'): ?>
                                        <li ><a href="<?php echo e(route('testimonials')); ?>"><?php echo e(__('Testimonial')); ?></a></li>
                                        <?php endif; ?>

                                        <?php if($menu_arr['Pricing'] == 'Show'): ?>
                                        <li ><a href="<?php echo e(route('pricing_plans')); ?>"><?php echo e(__('Pricing')); ?></a></li>
                                        <?php endif; ?>

                                        <?php if($menu_arr['FAQ'] == 'Show'): ?>
                                        <li ><a href="<?php echo e(route('faqs')); ?>"><?php echo e(__('FAQ')); ?></a></li>
                                        <?php endif; ?>

                                        <?php $__currentLoopData = $global_custom_pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li ><a href="<?php echo e(route('custom_page',$item->slug)); ?>"><?php echo e($item->name); ?></a></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </li>

                                <?php if($menu_arr['Services'] == 'Show'): ?>
                                <li class="<?php echo e(Request::is('services') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('services')); ?>"><?php echo e(__('Services')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($menu_arr['Portfolios'] == 'Show'): ?>
                                <li class="<?php echo e(Request::is('portfolios') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('portfolios')); ?>"><?php echo e(__('Portfolios')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($menu_arr['Blog'] == 'Show'): ?>
                                <li class="<?php echo e(Request::is('blog') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('blog')); ?>"><?php echo e(__('Blog')); ?></a>
                                </li>
                                <?php endif; ?>

                                <?php if($menu_arr['Contact'] == 'Show'): ?>
                                <li class="<?php echo e(Request::is('contact') ? 'current' : ''); ?>">
                                    <a href="<?php echo e(route('contact')); ?>"><?php echo e(__('Contact')); ?></a>
                                </li>
                                <?php endif; ?>
                                
                                <?php
                                $total_lang = \App\Models\Language::count();
                                ?>
                                <?php if($total_lang > 1): ?>
                                <li class="lang">
                                    <img class="globe" src="<?php echo e(asset('uploads/globe.png')); ?>" alt="">
                                    <img class="globe-black" src="<?php echo e(asset('uploads/globe-black.png')); ?>" alt="">
                                    <form action="<?php echo e(route('language_switch')); ?>" method="post">
                                        <?php echo csrf_field(); ?>
                                        <select name="code" class="form-control" onchange="this.form.submit()">
                                            <?php $__currentLoopData = $global_languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $global_language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($global_language->code); ?>" <?php if($global_language->name == session('sess_lang_name')): ?> selected <?php endif; ?>><?php echo e($global_language->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </form>
                                </li>
                                <?php endif; ?>
                                
                            </ul>
                        </nav>
                    </div>

                    <div class="outer-box">
                        <!-- Header Search -->
                        <button class="ui-btn ui-btn search-btn">
                            <span class="icon lnr lnr-icon-search"></span>
                        </button>
                        <?php if($global_setting->top_bar_phone!=''): ?>
                        <a href="tel:<?php echo e($global_setting->top_bar_phone); ?>" class="info-btn">
                            <i class="icon lnr-icon-phone-handset"></i>
                            <small ><?php echo e(__('Call Anytime')); ?></small>
                            <?php echo e($global_setting->top_bar_phone); ?>

                        </a>
                        <?php endif; ?>
                        <!-- Mobile Nav toggler -->
                        <div class="mobile-nav-toggler"><span class="icon lnr-icon-bars"></span></div>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu  -->
            <div class="mobile-menu">
                <div class="menu-backdrop"></div>
            
                <!--Here Menu Will Come Automatically Via Javascript / Same Menu as in Header-->
                <nav class="menu-box">
                    <div class="upper-box">
                        <div class="nav-logo"><a href="<?php echo e(route('home')); ?>"><img src="<?php echo e(asset('uploads/'.$global_setting->logo)); ?>" alt="<?php echo e(env('APP_NAME')); ?>" title=""></a></div>
                        <div class="close-btn"><i class="icon fa fa-times"></i></div>
                    </div>
            
                    <ul class="navigation clearfix">
                        <!--Keep This Empty / Menu will come through Javascript-->
                    </ul>

                    <?php if($global_setting->top_bar_phone!='' || $global_setting->top_bar_email!=''): ?>
                    <ul class="contact-list-one">
                        <?php if($global_setting->top_bar_phone != ''): ?>
                        <li >
                            <!-- Contact Info Box -->
                            <div class="contact-info-box">
                                <i class="icon lnr-icon-phone-handset"></i>
                                <span class="title"><?php echo e(__('Call Now')); ?></span>
                                <a href="tel:<?php echo e($global_setting->top_bar_phone); ?>"><?php echo e($global_setting->top_bar_phone); ?></a>
                            </div>
                        </li>
                        <?php endif; ?>
                        <?php if($global_setting->top_bar_email != ''): ?>
                        <li >
                            <!-- Contact Info Box -->
                            <div class="contact-info-box">
                                <span class="icon lnr-icon-envelope1"></span>
                                <span class="title"><?php echo e(__('Send Email')); ?></span>
                                <a href="mailto:<?php echo e($global_setting->top_bar_email); ?>"><?php echo e($global_setting->top_bar_email); ?></a>
                            </div>
                        </li>
                        <?php endif; ?>
                    </ul>
                    <?php endif; ?>

                    <?php if($global_setting->twitter!=''||$global_setting->facebook!=''||$global_setting->linkedin!=''||$global_setting->instagram!=''||$global_setting->youtube!=''||$global_setting->pinterest): ?>
                    <ul class="social-links">
                        <?php if($global_setting->twitter!=''): ?>
                        <li ><a href="<?php echo e($global_setting->twitter); ?>"><i class="fab fa-twitter"></i></a></li>
                        <?php endif; ?>

                        <?php if($global_setting->facebook!=''): ?>
                        <li ><a href="<?php echo e($global_setting->facebook); ?>"><i class="fab fa-facebook-f"></i></a></li>
                        <?php endif; ?>

                        <?php if($global_setting->linkedin!=''): ?>
                        <li ><a href="<?php echo e($global_setting->linkedin); ?>"><i class="fab fa-linkedin-in"></i></a></li>
                        <?php endif; ?>

                        <?php if($global_setting->instagram!=''): ?>
                        <li ><a href="<?php echo e($global_setting->instagram); ?>"><i class="fab fa-instagram"></i></a></li>
                        <?php endif; ?>

                        <?php if($global_setting->youtube!=''): ?>
                        <li ><a href="<?php echo e($global_setting->youtube); ?>"><i class="fab fa-youtube"></i></a></li>
                        <?php endif; ?>

                        <?php if($global_setting->pinterest!=''): ?>
                        <li ><a href="<?php echo e($global_setting->pinterest); ?>"><i class="fab fa-pinterest-p"></i></a></li>
                        <?php endif; ?>
                    </ul>
                    <?php endif; ?>
                </nav>
            </div><!-- End Mobile Menu -->

            <!-- Header Search -->
            <div class="search-popup">
                <span class="search-back-drop"></span>
                <button class="close-search"><span class="fa fa-times"></span></button>
            
                <div class="search-inner">
                    <form method="get" action="<?php echo e(route('search')); ?>">
                        <div class="form-group">
                            <input type="search" name="search_text" value="" placeholder="Search..." required>
                            <button type="submit"><i class="fa fa-search"></i></button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- End Header Search -->

            <!-- Sticky Header  -->
            <div class="sticky-header">
                <div class="auto-container">
                    <div class="inner-container">
                        <!--Logo-->
                        <div class="logo">
                            <a href="<?php echo e(route('home')); ?>">
                                <?php if(Route::is('home_4')): ?>
                                    <img src="<?php echo e(asset('uploads/'.$global_setting->logo)); ?>" alt="<?php echo e(env('APP_NAME')); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('uploads/'.$global_setting->logo_sticky)); ?>" alt="<?php echo e(env('APP_NAME')); ?>">
                                <?php endif; ?>
                            </a>
                        </div>
            
                        <!--Right Col-->
                        <div class="nav-outer">
                            <!-- Main Menu -->
                            <nav class="main-menu">
                                <div class="navbar-collapse show collapse clearfix">
                                    <ul class="navigation clearfix">
                                        <!--Keep This Empty / Menu will come through Javascript-->
                                    </ul>
                                </div>
                            </nav><!-- Main Menu End-->
            
                            <!--Mobile Navigation Toggler-->
                            <div class="mobile-nav-toggler"><span class="icon lnr-icon-bars"></span></div>
                        </div>
                    </div>
                </div>
            </div><!-- End Sticky Menu -->
        </header>
        <!--End Main Header -->


        <?php echo $__env->yieldContent('content'); ?>

        <!-- Main Footer -->
        <footer class="main-footer">
            <div class="bg bg-pattern-6"></div>
            
            <?php if($global_setting->footer_phone != '' || $global_setting->footer_email != '' || $global_setting->footer_address != ''): ?>
            <div class="footer-upper">
                <div class="auto-container">
                    <div class="row">
                        <?php if($global_setting->footer_phone != ''): ?>
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-phone-square"></i>
                                <span class="sub-title"><?php echo e(__('Call Anytime')); ?></span>
                                <div class="text"><a href="tel:+<?php echo e($global_setting->footer_phone); ?>"><?php echo e($global_setting->footer_phone); ?></a></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($global_setting->footer_email != ''): ?>
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-envelope"></i>
                                <span class="sub-title"><?php echo e(__('Send Email')); ?></span>
                                <div class="text"><a href="mailto:<?php echo e($global_setting->footer_email); ?>"><?php echo e($global_setting->footer_email); ?></a></div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if($global_setting->footer_address != ''): ?>
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-map-marker"></i>
                                <span class="sub-title"><?php echo e(__('Address')); ?></span>
                                <div class="text"><?php echo e($global_setting->footer_address); ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Widgets Section -->
            <div class="widgets-section">
                <div class="auto-container">
                    <div class="row">
                        <!-- Footer COlumn -->
                        <div class="footer-column col-xl-5 col-lg-4 col-md-12">
                            <div class="footer-widget about-widget">
                                <div class="widget-content">
                                    <div class="logo"><a href="<?php echo e(route('home')); ?>"><img src="<?php echo e(asset('uploads/'.$global_setting->logo)); ?>" alt="<?php echo e(env('APP_NAME')); ?>"></a></div>
                                    <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_setting->footer_text))); ?></div>
                                    <?php if($global_setting->twitter!=''||$global_setting->facebook!=''||$global_setting->linkedin!=''||$global_setting->instagram!=''||$global_setting->youtube!=''||$global_setting->pinterest): ?>
                                    <ul class="social-icon-two">
                                        <?php if($global_setting->twitter!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->twitter); ?>"><i class="fab fa-twitter"></i></a></li>
                                        <?php endif; ?>

                                        <?php if($global_setting->facebook!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->facebook); ?>"><i class="fab fa-facebook-f"></i></a></li>
                                        <?php endif; ?>

                                        <?php if($global_setting->linkedin!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->linkedin); ?>"><i class="fab fa-linkedin-in"></i></a></li>
                                        <?php endif; ?>

                                        <?php if($global_setting->instagram!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->instagram); ?>"><i class="fab fa-instagram"></i></a></li>
                                        <?php endif; ?>

                                        <?php if($global_setting->youtube!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->youtube); ?>"><i class="fab fa-youtube"></i></a></li>
                                        <?php endif; ?>

                                        <?php if($global_setting->pinterest!=''): ?>
                                        <li ><a href="<?php echo e($global_setting->pinterest); ?>"><i class="fab fa-pinterest-p"></i></a></li>
                                        <?php endif; ?>
                                    </ul>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Footer COlumn -->
                        <div class="footer-column col-xl-4 col-lg-4 col-md-6">
                            <div class="widget links-widget">
                                <h5 class="widget-title"><?php echo e($global_setting->footer_links_heading); ?></h5>
                                <div class="widget-content">
                                    <ul class="user-links two-column">
                                        <li ><a href="<?php echo e(route('about')); ?>"><?php echo e(__('About')); ?></a></li>
                                        <li ><a href="<?php echo e(route('team_members')); ?>"><?php echo e(__('Team Members')); ?></a></li>
                                        <li ><a href="<?php echo e(route('services')); ?>"><?php echo e(__('Services')); ?></a></li>
                                        <li ><a href="<?php echo e(route('testimonials')); ?>"><?php echo e(__('Testimonials')); ?></a></li>
                                        <li ><a href="<?php echo e(route('portfolios')); ?>"><?php echo e(__('Portfolios')); ?></a></li>
                                        <li ><a href="<?php echo e(route('faqs')); ?>"><?php echo e(__('FAQ')); ?></a></li>
                                        <li ><a href="<?php echo e(route('contact')); ?>"><?php echo e(__('Contact')); ?></a></li>
                                        <li ><a href="<?php echo e(route('terms')); ?>"><?php echo e(__('Terms of Use')); ?></a></li>
                                        <li ><a href="<?php echo e(route('pricing_plans')); ?>"><?php echo e(__('Pricing')); ?></a></li>
                                        <li ><a href="<?php echo e(route('privacy')); ?>"><?php echo e(__('Privacy Policy')); ?></a></li>
                                    </ul>                                
                                </div>
                            </div>
                        </div>
                        <div class="footer-column col-xl-3 col-lg-4 col-md-6 col-sm-12">
                            <div class="widget newsletter-widget">
                                <h5 class="widget-title"><?php echo e($global_setting->footer_subscriber_heading); ?></h5>
                                <div class="widget-content">
                                    <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_setting->footer_subscriber_text))); ?></div>
                                    <div class="subscribe-form">
                                        <form method="post" action="<?php echo e(route('subscriber_submit')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <div class="form-group">
                                                <input type="email" name="email" class="email" value="" placeholder="<?php echo e(__('Email Address')); ?>" required>
                                            </div>
                                            <div class="form-group">
                                                <button type="submit" class="theme-btn btn-style-one hover-light"><span class="btn-title"><?php echo e(__('Subscribe')); ?></span></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="auto-container">
                    <div class="copyright-text"><?php echo e($global_setting->footer_copyright); ?></div>
                </div>
            </div>
        </footer>

    </div>
    
    <div class="scroll-to-top scroll-to-target" data-target="html"><span class="fa fa-angle-up"></span></div>
    
    <?php echo $__env->make('front.layouts.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if($global_setting->tawk_live_chat_status == 'Show'): ?>
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/<?php echo e($global_setting->tawk_live_chat_property_id); ?>/default';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
    <?php endif; ?>

</body>
</html><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/layouts/master.blade.php ENDPATH**/ ?>