<link rel="stylesheet" href="<?php echo e(asset('dist-admin/fontawesome-free/css/all.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/bootstrap.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/fontawesome-iconpicker.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/sb-admin-2.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/jquery-ui.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/jquery.timepicker.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/select2.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/bootstrap5-toggle.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/dataTables.bootstrap5.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/magnific-popup.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/flaticon.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/spacing.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/style.css')); ?>">
<?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?>
<link rel="stylesheet" href="<?php echo e(asset('dist-admin/css/rtl.css')); ?>">
<?php endif; ?><?php /**PATH /home/<USER>/cms.pronixs.com/resources/views/admin/layouts/styles.blade.php ENDPATH**/ ?>