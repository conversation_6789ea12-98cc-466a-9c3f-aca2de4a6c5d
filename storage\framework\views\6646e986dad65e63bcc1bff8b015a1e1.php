<?php $__env->startSection('seo_title', $global_setting->home_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_setting->home_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>

<!-- Slider Section -->
<section class="banner-section">
    <div class="banner-carousel owl-carousel owl-theme default-navs">
        <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="slide-item">
            <div class="bg-image" style="background-image: url(<?php echo e(asset('uploads/'.$item->photo)); ?>);"></div>
            <div class="auto-container">
                <div class="content-box">
                    <?php if($item->text!=''): ?>
                    <h1 class="title animate-1">
                        <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($item->text))); ?>

                    </h1>
                    <?php endif; ?>
                    <?php if($item->button_text!=''): ?>
                    <div class="btn-box animate-2">
                        <a href="<?php echo e($item->button_url); ?>" class="theme-btn btn-style-one hover-light"><span class="btn-title"><?php echo e($item->button_text); ?></span></a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</section>
<!-- End Slider Section -->


<?php if($home_2_page_items->service_status == 'Show'): ?>
<section class="services-section-two">
    <div class="bg bg-pattern-12"></div>
    <div class="auto-container">
        <div class="sec-title text-center light">
            <span class="sub-title"><?php echo e($home_2_page_items->service_subheading); ?></span>
            <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->service_heading))); ?></h2>
        </div>
        <div class="row">
            <?php $__currentLoopData = $services->take($home_2_page_items->service_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="service-block-two col-lg-4 col-md-6 coll-md-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><img src="<?php echo e(asset('uploads/'.$service->photo)); ?>" alt=""></figure>
                    </div>
                    <div class="title-box">
                        <h5 class="title"><a href="<?php echo e(route('service',$service->slug)); ?>"><?php echo e($service->name); ?></a></h5>
                    </div>
                    <div class="content-box">
                        <i class="icon <?php echo e($service->icon); ?>"></i>
                        <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($service->short_description))); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->marquee_status == 'Show'): ?>
<div class="marquee-section">
    <div class="marquee">
        <div class="marquee-group">
            <?php $__currentLoopData = $marquees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="text"><?php echo e($item->item); ?></div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div aria-hidden="true" class="marquee-group">
            <?php $__currentLoopData = $marquees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="text"><?php echo e($item->item); ?></div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php endif; ?>


<?php if($home_2_page_items->welcome_status == 'Show'): ?>
<section class="about-section-two">
    <div class="anim-icons">
        <span class="icon icon-line4"></span>
        <span class="icon icon-line5"></span>
        <span class="icon icon-arrow1 bounce-x"></span>
        <span class="icon icon-speaker zoom-one"></span>
    </div>
    <div class="auto-container">
        <div class="outer-box">
            <div class="row">
                <div class="content-column col-xl-6 col-lg-7 col-md-12 col-sm-12 order-2 wow fadeInRight" data-wow-delay="600ms">
                    <div class="inner-column">
                        <div class="sec-title">
                            <span class="sub-title"><?php echo e($welcome_two_items->subheading); ?></span>
                            <h2><?php echo e($welcome_two_items->heading); ?></h2>
                            <div class="text">
                                <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($welcome_two_items->text))); ?>

                            </div>
                        </div>
                        <div class="row">
                            <?php $__currentLoopData = $welcome_two_item_elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="info-box col-lg-6 col-md-6">
                                <div class="inner">
                                    <h5 class="title"><i class="icon <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa fa-circle-arrow-left <?php else: ?> fa fa-circle-arrow-right <?php endif; ?>"></i> <?php echo e($item->heading); ?></h5>
                                    <div class="text">
                                        <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($item->text))); ?>

                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="skills">
                            <?php $__currentLoopData = $welcome_two_item_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="skill-item">
                                <div class="skill-header">
                                    <h5 class="skill-title"><?php echo e($item->name); ?></h5>
                                </div>
                                <div class="skill-bar">
                                    <div class="bar-inner">
                                        <div class="bar progress-line" data-width="<?php echo e($item->percentage); ?>">
                                            <div class="skill-percentage">
                                                <div class="count-box"><span class="count-text" data-speed="3000" data-stop="<?php echo e($item->percentage); ?>">0</span>%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="bottom-box">
                            <a href="<?php echo e($welcome_two_items->button_url); ?>" class="theme-btn btn-style-one hvr-dark"><span class="btn-title"><?php echo e($welcome_two_items->button_text); ?></span></a>
                        </div>
                    </div>
                </div>
                <div class="image-column col-xl-6 col-lg-5 col-md-12 col-sm-12">
                    <div class="inner-column wow fadeInLeft">
                        <div class="image-box">
                            <span class="icon-dots2"></span>
                            <figure class="image-1 overlay-anim wow fadeInUp"><img src="<?php echo e(asset('uploads/'.$welcome_two_items->photo1)); ?>" alt=""></figure>
                            <figure class="image-2 overlay-anim wow fadeInRight"><img src="<?php echo e(asset('uploads/'.$welcome_two_items->photo2)); ?>" alt=""></figure>
                            <div class="exp-box">
                                <div class="inner">
                                    <i class="icon flaticon-promotion"></i>
                                    <span class="count"><?php echo e($welcome_two_items->experience_year); ?></span>
                                    <h6 class="title"><?php echo e(__('Work Experience')); ?></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->portfolio_status == 'Show'): ?>
<section class="projects-section pt-0">
    <div class="auto-container">
        <div class="sec-title text-center">
            <span class="sub-title"><?php echo e($home_2_page_items->portfolio_subheading); ?></span>
            <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->portfolio_heading))); ?></h2>
        </div>
        <div class="outer-box">
            <div class="row">
                <?php $__currentLoopData = $portfolios->take($home_2_page_items->portfolio_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $portfolio): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class=" project-block col-lg-3 col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner-box">
                        <div class="image-box">
                            <figure class="image"><a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$portfolio->photo)); ?>" alt=""></a></figure>
                        </div>
                        <div class="content-box">
                            <a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>" class="icon"><i class="fa <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa-long-arrow-alt-left <?php else: ?> fa-long-arrow-alt-right <?php endif; ?>"></i></a>
                            <h4 class="title"><a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>" title=""><?php echo e($portfolio->name); ?></a></h4>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->why_choose_status == 'Show'): ?>
<section class="why-choose-us-two">
    <div class="anim-icons">
        <span class="icon icon-arrow1"></span>
    </div>
    <div class="auto-container">
        <div class="row">
            <div class="content-column col-lg-6 col-md-12">
                <div class="inner-column wow fadeInRight">
                    <div class="sec-title">
                        <i class="sub-title"><?php echo e($why_choose_two_items->subheading); ?></i>
                        <h2><?php echo e($why_choose_two_items->heading); ?></h2>
                    </div>
                    <div class="row">
                        <?php $__currentLoopData = $why_choose_two_item_elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="info-box col-lg-6 col-md-6">
                            <div class="inner">
                                <div class="title-box">
                                    <i class="icon <?php echo e($item->icon); ?>"></i>
                                    <h5 class="title">
                                        <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($item->heading))); ?>

                                    </h5>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <div class="image-column col-lg-6 col-md-12">
                <div class="inner-column">
                    <div class="image-box">
                        <figure class="image anim-overlay"><img src="<?php echo e(asset('uploads/'.$why_choose_two_items->photo)); ?>" alt=""></figure>
                        <div class="content-box">
                            <div class="text">
                                <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($why_choose_two_items->photo_over_text))); ?>

                            </div>
                            <div class="caption"><?php echo e($why_choose_two_items->photo_over_heading); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->testimonial_status == 'Show'): ?>
<section class="testimonial-section-two">
    <div class="bg bg-pattern-9"></div>
    <div class="auto-container">
        <div class="row">
            <div class="title-column col-xl-3 col-lg-4 col-md-12">
                <div class="inner-column">
                    <div class="sec-title">
                        <span class="sub-title"><?php echo e($home_2_page_items->testimonial_subheading); ?></span>
                        <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->testimonial_heading))); ?></h2>
                        <div class="text"><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->testimonial_text))); ?></div>
                    </div>
                </div>
            </div>
            <div class="testimonial-column col-xl-9 col-lg-8 col-md-12">
                <div class="inner-column">
                    <div class="testimonial-carousel-two owl-carousel default-navs">
                        <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="testimonial-block-two">
                            <div class="inner-box">
                                <div class="content-box">
                                    <figure class="thumb"><img src="<?php echo e(asset('uploads/'.$testimonial->photo)); ?>" alt=""></figure>
                                    <div class="rating">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= $testimonial->rating): ?>
                                                <i class="fas fa-star"></i>
                                            <?php else: ?>
                                                <i class="far fa-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="text">
                                        <?php echo clean(nl2br($testimonial->comment)); ?>

                                    </div>
                                    <div class="info-box">
                                        <h6 class="name"><?php echo e($testimonial->name); ?></h6>
                                        <span class="designation"><?php echo e($testimonial->designation); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->team_member_status == 'Show'): ?>
<section class="team-section pb-0">    
    <div class="auto-container">
        <div class="sec-title text-center">
            <span class="sub-title"><?php echo e($home_2_page_items->team_member_subheading); ?></span>
            <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->team_member_heading))); ?></h2>
        </div>
        <div class="row">
            <?php $__currentLoopData = $team_members->take($home_2_page_items->team_member_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="team-block-two col-lg-4 col-md-6 col-sm-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="info-box">
                        <h4 class="name"><a href="<?php echo e(route('team_member',$item->slug)); ?>"><?php echo e($item->name); ?></a></h4>
                        <span class="designation"><?php echo e($item->designation); ?></span>
                    </div>
                    <div class="image-box">
                        <figure class="image"><a href="<?php echo e(route('team_member',$item->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></a></figure> 
                        <div class="social-links">
                            <?php if($item->facebook != ''): ?>
                                <a href="<?php echo e($item->facebook); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if($item->twitter != ''): ?>
                                <a href="<?php echo e($item->twitter); ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                            <?php endif; ?>
                            <?php if($item->linkedin != ''): ?>
                                <a href="<?php echo e($item->linkedin); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                            <?php endif; ?>
                            <?php if($item->instagram != ''): ?>
                                <a href="<?php echo e($item->instagram); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                            <?php if($item->youtube != ''): ?>
                                <a href="<?php echo e($item->youtube); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                            <?php endif; ?>
                            <?php if($item->pinterest != ''): ?>
                                <a href="<?php echo e($item->pinterest); ?>" target="_blank"><i class="fab fa-pinterest-p"></i></a>
                            <?php endif; ?>
                        </div>
                        <span class="share-icon fas fa-plus"></span>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<?php if($home_2_page_items->client_status == 'Show'): ?>
<section class="clients-section">
    <div class="auto-container">
        <div class="sponsors-outer">
            <ul class="clients-carousel owl-carousel owl-theme">
                <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="client-block">
                    <?php if($item->url!=''): ?>
                    <a href="<?php echo e($item->url); ?>">
                        <img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt="">
                    </a>
                    <?php else: ?>
                    <a href="javascript:void;">
                        <img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt="">
                    </a>
                    <?php endif; ?>
                </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->contact_status == 'Show'): ?>
<section class="contact-section-two">
    <div class="bg bg-pattern-11"></div>
    <div class="image-box">
        <div class="image"><img src="<?php echo e(asset('uploads/'.$home_contact_photos->home_2_contact_photo)); ?>" alt=""></div>
        <div class="image-overlay"></div>
    </div>
    <div class="auto-container">
        <div class="row">
            <div class="form-column col-lg-7 col-md-12">
                <div class="inner-column">
                    <div class="sec-title light">
                        <span class="sub-title"><?php echo e($home_2_page_items->contact_subheading); ?></span>
                        <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->contact_heading))); ?></h2>
                    </div>
                    <div class="contact-form wow fadeInLeft">
                        <form method="post" action="<?php echo e(route('contact_send_message')); ?>" id="contact-form">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="form-group col-lg-6 col-md-12 col-sm-12">
                                    <input type="text" name="name" placeholder="<?php echo e(__('Full Name')); ?>" required>
                                </div>
                                <div class="form-group col-lg-6 col-md-12 col-sm-12">
                                    <input type="email" name="email" placeholder="<?php echo e(__('Email Address')); ?>" required>
                                </div>
                                <div class="form-group col-lg-12 col-md-12 col-sm-12">
                                    <input type="text" name="subject" placeholder="<?php echo e(__('Subject')); ?>" required>
                                </div>
                                <div class="form-group col-lg-12">
                                    <textarea name="message" placeholder="<?php echo e(__('Message')); ?>" required></textarea>
                                </div>
                                <?php if($global_setting->google_recaptcha_status == 'Show'): ?>
                                <div class="form-group col-lg-12">
                                    <div class="g-recaptcha" data-sitekey="<?php echo e($global_setting->google_recaptcha_site_key); ?>"></div>
                                </div>
                                <?php endif; ?>
                                <div class="form-group col-lg-12">
                                    <button class="theme-btn btn-style-one" type="submit" name="submit-form"><span class="btn-title"><?php echo e(__('Send Message')); ?></span></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>


<?php if($home_2_page_items->blog_status == 'Show'): ?>
<section class="news-section">
    <div class="auto-container">
        <div class="sec-title text-center">
            <span class="sub-title"><?php echo e($home_2_page_items->blog_subheading); ?></span>
            <h2><?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($home_2_page_items->blog_heading))); ?></h2>
        </div>
        <div class="row">
            <?php $__currentLoopData = $posts->take($home_2_page_items->blog_how_many); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="news-block col-lg-4 col-md-6 col-sm-12 wow fadeInUp">
                <div class="inner-box">
                    <div class="image-box">
                        <figure class="image"><a href="<?php echo e(route('post',$post->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$post->photo)); ?>" alt=""></a></figure>
                    </div>
                    <div class="content-box">
                        <span class="date"><?php echo e($post->created_at->format('d M, Y')); ?></span>
                        <ul class="post-info">
                            <li><i class="fa fa-user-circle"></i> <?php echo e(__('by Admin')); ?></li>
                        </ul>
                        <h4 class="title"><a href="<?php echo e(route('post',$post->slug)); ?>"><?php echo e($post->title); ?></a></h4>
                        <a href="<?php echo e(route('post',$post->slug)); ?>" class="read-more"><?php echo e(__('Read More')); ?> <i class="fa <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa-long-arrow-alt-left <?php else: ?> fa-long-arrow-alt-right <?php endif; ?>"></i></a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<?php if($home_2_page_items->map_status == 'Show'): ?>
<?php if($global_setting->map != ''): ?>
<section class="map-section">
    <?php echo $global_setting->map; ?>

</section>
<?php endif; ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/home-2.blade.php ENDPATH**/ ?>