<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Languages')); ?></h1>
    <a href="<?php echo e(route('admin_language_create')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-plus"></i> <?php echo e(__('Add Item')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-sm" id="dtable">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Name')); ?></th>
                        <th><?php echo e(__('Code')); ?></th>
                        <th><?php echo e(__('Direction')); ?></th>
                        <th><?php echo e(__('Default')); ?></th>
                        <th><?php echo e(__('Translate')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td><?php echo e($item->name); ?></td>
                        <td><?php echo e($item->code); ?></td>
                        <td><?php echo e($item->direction); ?></td>
                        <td>
                            <?php if($item->default == 1): ?>
                            <span class="badge badge-success"><?php echo e(__('Yes')); ?></span>
                            <?php else: ?>
                            <span class="badge badge-danger"><?php echo e(__('No')); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="<?php echo e(route('admin_language_translate',$item->id)); ?>" class="btn btn-info btn-sm">Translate</a>
                        </td>
                        <td>
                            <a href="<?php echo e(route('admin_language_edit',$item->id)); ?>" class="btn btn-primary btn-sm"><i class="fas fa-edit"></i></a>
                            <?php if($item->id!=1): ?>
                            <a href="<?php echo e(route('admin_language_destroy',$item->id)); ?>" class="btn btn-danger btn-sm" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><i class="fas fa-trash"></i></a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/language/index.blade.php ENDPATH**/ ?>