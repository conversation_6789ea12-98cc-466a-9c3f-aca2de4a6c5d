<?php $__env->startSection('seo_title', $service->seo_title); ?>
<?php $__env->startSection('seo_meta_description', $service->seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($service->name); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><a href="<?php echo e(route('services')); ?>"><?php echo e($global_other_page_items->page_services_title); ?></a></li>
                <li><?php echo e($service->name); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="services-details">
    <div class="container">
        <div class="row">
            <div class="col-xl-4 col-lg-4">
                <div class="service-sidebar">
                    <div class="sidebar-widget service-sidebar-single">
                        <div class="sidebar-service-list">
                            <ul>
                                <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a href="<?php echo e(route('service',$item->slug)); ?>" class="current"><i class="fas <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa-angle-left <?php else: ?> fa-angle-right <?php endif; ?>"></i><span><?php echo e($item->name); ?></span></a></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>

                        <?php if($service->phone != null): ?>
                        <div class="service-details-help">
                            <div class="help-shape-1"></div>
                            <div class="help-shape-2"></div>
                            <h2 class="help-title"><?php echo e(__('Contact with us for any advice')); ?></h2>
                            <div class="help-icon">
                                <span class=" lnr-icon-phone-handset"></span>
                            </div>
                            <div class="help-contact">
                                <p><?php echo e(__('Need help? Talk to an expert')); ?></p>
                                <a href="tel:<?php echo e($service->phone); ?>"><?php echo e($service->phone); ?></a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if($service->pdf != null): ?>
                        <div class="sidebar-widget service-sidebar-single mt-4">
                            <div class="service-sidebar-single-btn wow fadeInUp" data-wow-delay="0.5s" data-wow-duration="1200m">
                                <a href="<?php echo e(asset('uploads/'.$service->pdf)); ?>" class="theme-btn btn-style-one d-grid"><span class="btn-title"><span class="fas fa-file-pdf"></span> <?php echo e(__('Download PDF File')); ?></span></a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-xl-8 col-lg-8">
                <div class="services-details__content">
                    <?php if($service->banner != null): ?>
                        <img src="<?php echo e(asset('uploads/'.$service->banner)); ?>" alt="">
                    <?php endif; ?>
                    <h3 class="mt-4"><?php echo e(__('Service Overview')); ?></h3>
                    <div class="content mt-40">
                        <div class="text">
                            <p>
                                <?php echo clean($service->description); ?>

                            </p>
                        </div>
                    </div>
                    <div class=" mt-25">
                        <h3><?php echo e(__('Frequently Asked Questions')); ?></h3>
                        <ul class="accordion-box wow fadeInRight">                            
                            <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="accordion block">
                                <div class="acc-btn"><?php echo e($faq->question); ?>

                                    <div class="icon fa fa-plus"></div>
                                </div>
                                <div class="acc-content">
                                    <div class="content">
                                        <div class="text">
                                            <p><?php echo clean($faq->answer); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/front/service.blade.php ENDPATH**/ ?>