<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit Service')); ?></h1>
    <a href="<?php echo e(route('admin_service_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_service_update',$service->id)); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Name')); ?> *</label>
                        <input type="text" name="name" class="form-control" value="<?php echo e($service->name); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Slug')); ?>*</label>
                        <input type="text" name="slug" class="form-control" value="<?php echo e($service->slug); ?>">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Short Description')); ?> *</label>
                <textarea name="short_description" class="form-control h_100" cols="30" rows="10"><?php echo e($service->short_description); ?></textarea>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Description')); ?> * </label>
                <textarea name="description" class="form-control editor" cols="30" rows="10"><?php echo e($service->description); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Icon')); ?>*</label>
                        <select id="iconSelect" name="icon" class="form-select">
                            <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($icon->icon_code); ?>" <?php if($icon->icon_code==$service->icon): ?> selected <?php endif; ?>><?php echo e($icon->icon_code); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <div id="iconPreview"><i class="icon <?php echo e($service->icon); ?>"></i></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Phone')); ?></label>
                        <input type="text" name="phone" class="form-control" value="<?php echo e($service->phone); ?>">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Title')); ?></label>
                <input type="text" name="seo_title" class="form-control" value="<?php echo e($service->seo_title); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Meta Description')); ?></label>
                <textarea name="seo_meta_description" class="form-control h_100" cols="30" rows="10"><?php echo e($service->seo_meta_description); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                        <div class="photo-container">
                            <a href="<?php echo e(asset('uploads/'.$service->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$service->photo)); ?>" alt=""></a>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                        <div><input type="file" name="photo"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Banner')); ?></label>
                        <div class="photo-container">
                            <?php if($service->banner==null): ?>
                                <img src="<?php echo e(asset('uploads/no_photo.png')); ?>" alt="">
                            <?php else: ?>
                                <a href="<?php echo e(asset('uploads/'.$service->banner)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$service->banner)); ?>" alt=""></a>
                                <div><a href="<?php echo e(route('admin_service_destroy_banner',$service->id)); ?>" class="text-danger" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><?php echo e(__('Delete Banner')); ?></a></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Banner')); ?></label>
                        <div><input type="file" name="banner"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing PDF')); ?></label>
                        <div class="photo-container">
                            <?php if($service->pdf==null): ?>
                                <img src="<?php echo e(asset('uploads/no_pdf.png')); ?>" alt="">
                            <?php else: ?>
                                <a href="<?php echo e(asset('uploads/'.$service->pdf)); ?>"><img src="<?php echo e(asset('uploads/pdf.svg')); ?>" alt=""></a>
                                <div><a href="<?php echo e(route('admin_service_destroy_pdf',$service->id)); ?>" class="text-danger" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><?php echo e(__('Delete PDF')); ?></a></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change PDF')); ?></label>
                        <div><input type="file" name="pdf"></div>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
        </form>
    </div>
</div>
<script>
    window.onload = function () {
        document.getElementById('iconSelect').addEventListener('change', function () {
            var selectedValue = this.value;
            var previewElement = document.getElementById('iconPreview');
            previewElement.innerHTML = '<i class="icon ' + selectedValue + '"></i>';
        });
    };
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/service/edit.blade.php ENDPATH**/ ?>