<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Services')); ?></h1>
    <a href="<?php echo e(route('admin_service_create')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-plus"></i> <?php echo e(__('Add Item')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-sm" id="dtable">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Photo')); ?></th>
                        <th><?php echo e(__('Banner')); ?></th>
                        <th><?php echo e(__('Icon')); ?></th>
                        <th><?php echo e(__('Name')); ?></th>
                        <th><?php echo e(__('Manage FAQ')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td>
                            <div class="photo-container-small">
                                <a href="<?php echo e(asset('uploads/'.$service->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$service->photo)); ?>" alt=""></a>
                            </div>
                        </td>
                        <td>
                            <div class="photo-container-small">
                                <?php if($service->banner==null): ?>
                                    <img src="<?php echo e(asset('uploads/no_photo.png')); ?>" alt="">
                                <?php else: ?>
                                    <a href="<?php echo e(asset('uploads/'.$service->banner)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$service->banner)); ?>" alt=""></a>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <i class="<?php echo e($service->icon); ?> fz_40"></i>
                        </td>
                        <td>
                            <?php echo e($service->name); ?>

                        </td>
                        <td>
                            <a href="<?php echo e(route('admin_service_faq',$service->id)); ?>" class="btn btn-success btn-sm rounded-pill pl_20 pr_20"><?php echo e(__('Manage FAQ')); ?></a>
                        </td>
                        <td>
                            <a href="<?php echo e(route('admin_service_edit',$service->id)); ?>" class="btn btn-primary btn-sm"><i class="fas fa-edit"></i></a>
                            <a href="<?php echo e(route('admin_service_destroy',$service->id)); ?>" class="btn btn-danger btn-sm" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><i class="fas fa-trash"></i></a>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/service/index.blade.php ENDPATH**/ ?>