<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Dashboard')); ?></h1>
</div>

<div class="row dashboard-page">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Post Categories')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_post_categories); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Posts')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_posts); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Testimonials')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_testimonials); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Team Members')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_team_members); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Services')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_services); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Portfolios')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_portfolios); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Sliders')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_sliders); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total FAQs')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_faqs); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Subscribers')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_subscribers); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Pricing Plans')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_pricing_plans); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Custom Pages')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_custom_pages); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="h4 font-weight-bold text-success mb-1"><?php echo e(__('Total Marquees')); ?></div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($total_marquees); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fas fa-th-large fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>