<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Video Two - Items')); ?></h1>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card shadow mb-4">
            <div class="card-body">
                <form action="<?php echo e(route('admin_video_two_item_update')); ?>" method="post" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Heading')); ?>*</label>
                                <textarea name="heading" class="form-control h_70" cols="30" rows="10"><?php echo e($video_two_items->heading); ?></textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Youtube Video Id')); ?> *</label>
                                <input type="text" name="youtube_video_id" class="form-control" value="<?php echo e($video_two_items->youtube_video_id); ?>">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/video/two.blade.php ENDPATH**/ ?>