<?php $__env->startSection('content'); ?>

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Translation')); ?> - <?php echo e($language_data->name); ?></h1>
    <a href="" class="d-none d-sm-inline-block btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#exampleModal"><i class="fas fa-globe"></i> <?php echo e(__('Auto Translate')); ?>

    </a>
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel"><?php echo e(__('Auto Translate Confirmation')); ?></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?php echo e(__('Are you sure? Remember, it may take a little longer to do so.')); ?>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"><?php echo e(__('No')); ?></button>
                    <a href="<?php echo e(route('admin_language_auto_translate',$language_data->id)); ?>" class="btn btn-success"><?php echo e(__('Yes')); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_language_translate_update',$language_data->id)); ?>" method="post">
            <?php echo csrf_field(); ?>
            <div class="table-responsive">
                <table class="table table-bordered table-sm" id="" width="100%" cellspacing="0">
                    <thead>
                    <tr>
                        <th class="w_50_p"><?php echo e(__('Key')); ?></th>
                        <th><?php echo e(__('Value')); ?></th>
                    </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $translation_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <input type="hidden" class="form-control" name="key_arr[]" value="<?php echo e($key); ?>">
                        <tr>
                            <td>
                                <?php echo e($key); ?>

                            </td>
                            <td>
                                <input type="text" name="value_arr[]" class="form-control" value="<?php echo e($value); ?>">
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/language/translate.blade.php ENDPATH**/ ?>