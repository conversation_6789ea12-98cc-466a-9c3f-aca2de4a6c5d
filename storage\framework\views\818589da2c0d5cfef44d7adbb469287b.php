<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Call to Action')); ?></h1>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card shadow mb-4">
            <div class="card-body">
                <form action="<?php echo e(route('admin_call_to_action_update')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Text')); ?></label>
                        <textarea name="text" class="form-control h_100" cols="30" rows="10"><?php echo e($call_to_action->text); ?></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Phone')); ?></label>
                                <input type="text" name="phone" class="form-control" value="<?php echo e($call_to_action->phone); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Email')); ?></label>
                                <input type="text" name="email" class="form-control" value="<?php echo e($call_to_action->email); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Icon')); ?></label>
                                <select id="iconSelectEdit" name="icon" class="form-select">
                                    <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($icon->icon_code); ?>" <?php if($icon->icon_code==$call_to_action->icon): ?> selected <?php endif; ?>><?php echo e($icon->icon_code); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <div id="iconPreviewEdit"><i class="icon <?php echo e($call_to_action->icon); ?>"></i></div>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>
       
<script>
    window.onload = function () {
        document.getElementById('iconSelectEdit').addEventListener('change', function () {
            var selectedValueEdit = this.value;
            var previewElementEdit = document.getElementById('iconPreviewEdit');
            previewElementEdit.innerHTML = '<i class="icon ' + selectedValueEdit + '"></i>';
        });
    };
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/call_to_action/index.blade.php ENDPATH**/ ?>