<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/png" href="uploads/favicon.png">
    
    <title><?php echo e(__('Login')); ?></title>

    <link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <?php echo $__env->make('admin.layouts.styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin.layouts.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body class="bg-gradient-primary d-flex align-items-center h_100_vh">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <div class="row">
                            <div class="col-lg-6 d-none d-lg-block login_bg" style="background-image:url(<?php echo e(asset('uploads/'.$global_setting->login_page_photo)); ?>)"></div>
                            <div class="col-lg-6">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4 fw-bold"><?php echo e(__('Admin Login')); ?></h1>
                                    </div>
                                    <form action="<?php echo e(route('admin_login_submit')); ?>" class="user" method="post">
                                        <?php echo csrf_field(); ?>
                                        <div class="form-group">
                                            <input type="email" name="email" class="form-control form-control-user" placeholder="<?php echo e(__('Email Address')); ?>" value="<?php if(env('PROJECT_MODE') == 0): ?><?php echo e(('<EMAIL>')); ?> <?php endif; ?>">
                                        </div>
                                        <div class="form-group">
                                            <input type="password" name="password" class="form-control form-control-user" placeholder="<?php echo e(__('Password')); ?>" value="<?php if(env('PROJECT_MODE') == 0): ?><?php echo e(('1234')); ?><?php endif; ?>">
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-user btn-block">
                                            <?php echo e(__('Login')); ?>

                                        </button>
                                    </form>
                                    <hr>
                                    <div class="text-center">
                                        <a class="" href="<?php echo e(route('admin_forget_password')); ?>"><?php echo e(__('Forget Password')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('admin.layouts.scripts_footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>
</html>
<?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/auth/login.blade.php ENDPATH**/ ?>