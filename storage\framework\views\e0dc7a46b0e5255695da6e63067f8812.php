<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Posts')); ?></h1>
    <a href="<?php echo e(route('admin_post_create')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-plus"></i> <?php echo e(__('Add Item')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-sm" id="dtable">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Photo')); ?></th>
                        <th><?php echo e(__('Title')); ?></th>
                        <th><?php echo e(__('Category')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td>
                            <div class="photo-container">
                                <a href="<?php echo e(asset('uploads/'.$item->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></a>
                            </div>
                        </td>
                        <td><?php echo e($item->title); ?></td>
                        <td><?php echo e($item->rPostCategory->name); ?></td>
                        <td>
                            <a href="<?php echo e(route('admin_post_edit',$item->id)); ?>" class="btn btn-primary btn-sm"><i class="fas fa-edit"></i></a>
                            <a href="<?php echo e(route('admin_post_destroy',$item->id)); ?>" class="btn btn-danger btn-sm" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><i class="fas fa-trash"></i></a>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.pronixs.com/resources/views/admin/post/index.blade.php ENDPATH**/ ?>