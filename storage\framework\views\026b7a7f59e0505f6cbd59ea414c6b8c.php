<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Menu Manage')); ?></h1>
</div>

<form action="<?php echo e(route('admin_menu_update')); ?>" method="post">
    <?php echo csrf_field(); ?>
    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" id="" width="100%" cellspacing="0">
                            <thead>
                            <tr>
                                <th><?php echo e(__('SL')); ?></th>
                                <th><?php echo e(__('Menu Name')); ?></th>
                                <th><?php echo e(__('Menu Status')); ?></th>
                            </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <input type="hidden" name="menu_id[]" value="<?php echo e($row->id); ?>">
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><?php echo e($row->name); ?></td>
                                    <td>
                                        <select name="status[]" class="form-select">
                                            <option value="Show" <?php if($row->status == 'Show'): ?> selected <?php endif; ?>><?php echo e(__('Show')); ?></option>
                                            <option value="Hide" <?php if($row->status == 'Hide'): ?> selected <?php endif; ?>><?php echo e(__('Hide')); ?></option>
                                        </select>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Update')); ?></button>
                </div>
            </div>
        </div>
    </div>
</form>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/menu/index.blade.php ENDPATH**/ ?>