<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Create Post')); ?></h1>
    <a href="<?php echo e(route('admin_post_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_post_store')); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Title')); ?> *</label>
                        <input type="text" name="title" class="form-control" value="<?php echo e(old('title')); ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Slug')); ?>*</label>
                        <input type="text" name="slug" class="form-control" value="<?php echo e(old('slug')); ?>">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('Description')); ?> *</label>
                <textarea name="description" class="form-control editor" cols="30" rows="10"><?php echo e(old('description')); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label><?php echo e(__('Select Category')); ?> *</label>
                        <select name="post_category_id" class="form-select">
                            <?php $__currentLoopData = $post_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($item->id); ?>" <?php if(old('post_category_id') == $item->id): ?> selected <?php endif; ?>><?php echo e($item->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label><?php echo e(__('Tags')); ?></label>
                        <select name="tags[]" class="form-select select2_tags"></select>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Title')); ?></label>
                <input type="text" name="seo_title" class="form-control" value="<?php echo e(old('seo_title')); ?>">
            </div>
            <div class="mb-3">
                <label for="" class="form-label"><?php echo e(__('SEO Meta Description')); ?></label>
                <textarea name="seo_meta_description" class="form-control h_100" cols="30" rows="10"><?php echo e(old('seo_meta_description')); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Photo')); ?> *</label>
                        <div><input type="file" name="photo"></div>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Submit')); ?></button>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.pronixs.com/resources/views/admin/post/create.blade.php ENDPATH**/ ?>