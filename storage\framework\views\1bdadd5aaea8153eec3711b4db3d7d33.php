<?php $__env->startSection('seo_title', $global_other_page_items->page_portfolios_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_portfolios_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_portfolios_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_portfolios_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="projects-section">
    <div class="auto-container">
        <div class="">
            <div class="row">
                <?php $__currentLoopData = $portfolios; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $portfolio): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class=" project-block col-lg-4 col-md-6 col-sm-12">
                    <div class="inner-box">
                        <div class="image-box">
                            <figure class="image"><a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>"><img src="<?php echo e(asset('uploads/'.$portfolio->photo)); ?>" alt="<?php echo e($portfolio->name); ?>"></a></figure>
                        </div>
                        <div class="content-box">
                            <a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>" class="icon"><i class="fa <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> fa-long-arrow-alt-left <?php else: ?> fa-long-arrow-alt-right <?php endif; ?>"></i></a>
                            <h4 class="title"><a href="<?php echo e(route('portfolio',$portfolio->slug)); ?>" title="<?php echo e($portfolio->name); ?>"><?php echo e($portfolio->name); ?></a></h4>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/portfolios.blade.php ENDPATH**/ ?>