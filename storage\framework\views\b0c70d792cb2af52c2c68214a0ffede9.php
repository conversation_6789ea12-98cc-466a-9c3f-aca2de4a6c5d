<?php $__env->startSection('seo_title', $team_member->seo_title); ?>
<?php $__env->startSection('seo_meta_description', $team_member->seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($team_member->name); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><a href="<?php echo e(route('team_members')); ?>"><?php echo e($global_other_page_items->page_team_members_title); ?></a></li>
                <li><?php echo e($team_member->name); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="team-details">
    <div class=""></div>
    <div class="container pb-100">
        <div class="team-details__top pb-70">
            <div class="row">
                <div class="col-xl-6 col-lg-6">
                    <div class="team-details__top-left">
                        <div class="team-details__top-img"> <img src="<?php echo e(asset('uploads/'.$team_member->photo)); ?>" alt="">
                            <div class="team-details__big-text"></div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 col-lg-6">
                    <div class="team-details__top-right">
                        <div class="team-details__top-content">
                            <h3 class="team-details__top-name"><?php echo e($team_member->name); ?></h3>
                            <p class="team-details__top-title"><?php echo e($team_member->designation); ?></p>
                            <p class="team-details__top-text-1">
                                <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($team_member->tagline))); ?>

                            </p>
                            <div class="team-details-contact mb-30">
                                <h5 class="mb-0"><?php echo e(__('Email Address')); ?></h5>
                                <div class=""><span><?php echo e($team_member->email); ?></span></div>
                            </div>
                            <div class="team-details-contact mb-30">
                                <h5 class="mb-0"><?php echo e(__('Phone Number')); ?></h5>
                                <div class=""><span><?php echo e($team_member->phone); ?></span></div>
                            </div>
                            <div class="team-details-contact">
                                <h5 class="mb-0"><?php echo e(__('Website')); ?></h5>
                                <div class=""><span><?php echo e($team_member->website); ?></span></div>
                            </div>

                            <?php if($team_member->facebook != '' || $team_member->twitter != '' || $team_member->linkedin != '' || $team_member->instagram != '' || $team_member->youtube != '' || $team_member->pinterest != ''): ?>
                            <div class="team-details__social">
                                <?php if($team_member->facebook != ''): ?>
                                    <a href="<?php echo e($team_member->facebook); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if($team_member->twitter != ''): ?>
                                    <a href="<?php echo e($team_member->twitter); ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                                <?php endif; ?>
                                <?php if($team_member->linkedin != ''): ?>
                                    <a href="<?php echo e($team_member->linkedin); ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <?php endif; ?>
                                <?php if($team_member->instagram != ''): ?>
                                    <a href="<?php echo e($team_member->instagram); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                                <?php if($team_member->youtube != ''): ?>
                                    <a href="<?php echo e($team_member->youtube); ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                                <?php endif; ?>
                                <?php if($team_member->pinterest != ''): ?>
                                    <a href="<?php echo e($team_member->pinterest); ?>" target="_blank"><i class="fab fa-pinterest-p"></i></a>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="team-details__bottom pt-100">
            <div class="row">
                <div class="col-xl-6 col-lg-6">
                    <div class="team-details__bottom-left">
                        <h4 class="team-details__bottom-left-title"><?php echo e(__('Personal Experience')); ?></h4>
                        <p class="team-details__bottom-left-text">
                            <?php echo str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($team_member->experience_text))); ?>

                        </p>
                    </div>
                </div>
                <div class="col-xl-6 col-lg-6">
                    <div class="team-details__bottom-right">
                        <div class="team-details__progress">
                            <?php $__currentLoopData = $experiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="team-details__progress-single">
                                <h4 class="team-details__progress-title"><?php echo e($item->name); ?></h4>
                                <div class="bar">
                                    <div class="bar-inner count-bar" data-percent="<?php echo e($item->percentage); ?>%">
                                        <div class="count-text"><?php echo e($item->percentage); ?>%</div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--Team Details End--> 

<!--Contact Details Start-->
<section class="team-contact-form">
    <div class="container pb-100">
        <div class="sec-title text-center">
            <span class="sub-title"><?php echo e(__('Contact With Me')); ?></span>
            <h2 class="section-title__title"><?php echo e(__('Feel Free to Contact with Me')); ?></h2>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <form id="contact_form" name="contact_form" class="" action="<?php echo e(route('team_member_send_message')); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="team_member_email" value="<?php echo e($team_member->email); ?>">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <input name="name" class="form-control" type="text" placeholder="<?php echo e(__('Full Name')); ?>" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <input name="email" class="form-control required email" type="email" placeholder="<?php echo e(__('Email Address')); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="mb-3">
                                <input name="subject" class="form-control required" type="text" placeholder="<?php echo e(__('Subject')); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <textarea name="message" class="form-control required" rows="5" placeholder="<?php echo e(__('Message')); ?>" required></textarea>
                    </div>
                    <?php if($global_setting->google_recaptcha_status == 'Show'): ?>
                    <div class="mb-3">
                        <div class="g-recaptcha" data-sitekey="<?php echo e($global_setting->google_recaptcha_site_key); ?>"></div>
                    </div>
                    <?php endif; ?>
                    <div class="mb-3 text-center">
                        <button type="submit" class="theme-btn btn-style-one" data-loading-text="<?php echo e(__('Please wait...')); ?>"><span class="btn-title"><?php echo e(__('Send Message')); ?></span></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/team_member.blade.php ENDPATH**/ ?>