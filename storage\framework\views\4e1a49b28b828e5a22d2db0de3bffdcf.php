<?php $__env->startSection('seo_title', $global_other_page_items->page_pricing_seo_title); ?>
<?php $__env->startSection('seo_meta_description', $global_other_page_items->page_pricing_seo_meta_description); ?>

<?php $__env->startSection('content'); ?>
<section class="page-title" style="background-image: url(<?php echo e(asset('uploads/'.$global_setting->banner)); ?>);">
    <div class="auto-container">
        <div class="title-outer">
            <h1 class="title"><?php echo e($global_other_page_items->page_pricing_title); ?></h1>
            <ul class="page-breadcrumb">
                <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                <li><?php echo e($global_other_page_items->page_pricing_title); ?></li>
            </ul>
        </div>
    </div>
</section>
<section class="">
    <div class="container pb-70">
        <div class="row">
            <?php $__currentLoopData = $pricing_plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xl-4 col-lg-4">
                <div class="pricing-block">
                    <div class="inner-box">
                        <figure class="image"><img src="<?php echo e(asset('uploads/'.$item->photo)); ?>" alt=""></figure>
                        <div class="price-box">
                            <h4 class="price"><sup><?php echo e($global_setting->currency_symbol); ?></sup><?php echo e($item->price); ?></h4>
                            <span class="validaty">/ <?php echo e($item->period); ?></span>
                        </div>
                        <h4 class="title"><?php echo e($item->name); ?></h4>
                        <ul class="features">
                            <?php
                                $option_list = DB::table('pricing_plan_options')->where('pricing_plan_id', $item->id)->get();
                            ?>
                            <?php $__currentLoopData = $option_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($item1->name); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                        <div class="btn-box">
                            <a href="<?php echo e($item->button_url); ?>" class="theme-btn btn-style-one hvr-light"><span class="btn-title"><?php echo e($item->button_text); ?></span></a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/front/pricing_plans.blade.php ENDPATH**/ ?>