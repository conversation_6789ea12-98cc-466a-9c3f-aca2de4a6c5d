<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit Language')); ?></h1>
    <a href="<?php echo e(route('admin_language_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-body">
                <form action="<?php echo e(route('admin_language_update',$language->id)); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Name')); ?>*</label>
                        <input type="text" name="name" class="form-control" value="<?php echo e($language->name); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Code')); ?>*</label>
                        <input type="text" name="code" class="form-control" value="<?php echo e($language->code); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Direction')); ?>*</label>
                        <select name="direction" class="form-select">
                            <option value="Left to Right (LTR)" <?php echo e($language->direction == 'Left to Right (LTR)' ? 'selected' : ''); ?>><?php echo e(__('Left to Right (LTR)')); ?></option>
                            <option value="Right to Left (RTL)" <?php echo e($language->direction == 'Right to Left (RTL)' ? 'selected' : ''); ?>><?php echo e(__('Right to Left (RTL)')); ?></option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Default')); ?>*</label>
                        <select name="default" class="form-select">
                            <option value="0" <?php echo e($language->default == 0 ? 'selected' : ''); ?>><?php echo e(__('No')); ?></option>
                            <option value="1" <?php echo e($language->default == 1 ? 'selected' : ''); ?>><?php echo e(__('Yes')); ?></option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /opt/homebrew/var/www/phpscriptpoint/desix/desix/cms/resources/views/admin/language/edit.blade.php ENDPATH**/ ?>