<!DOCTYPE html>
<html @if(session('sess_lang_direction') == 'Right to Left (RTL)') dir="rtl" @endif lang="{{ session('sess_lang_code') }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <title >@yield('seo_title')</title>
    <meta name="description" content="@yield('seo_meta_description')">
    
    <link rel="shortcut icon" href="{{ asset('uploads/'.$global_setting->favicon) }}" type="image/x-icon">
    <link rel="icon" href="{{ asset('uploads/'.$global_setting->favicon) }}" type="image/x-icon">

    @include('front.layouts.styles')

    @yield('dark_mode')

    @if($global_setting->sticky_header == 'Hide')
    <style >
        .sticky-header.fixed-header {
            display: none;
        }
    </style>
    @endif

    @if($global_setting->tawk_live_chat_status == 'Show')
		<style >
		.scroll-to-top {
			bottom: 50px!important;
		}
		</style>
    @endif

    @if($global_setting->cookie_consent_status == 'Show')
        <script src="https://cdn.websitepolicies.io/lib/cookieconsent/1.0.3/cookieconsent.min.js" defer></script><script >window.addEventListener("load",function(){window.wpcc.init({"colors":{"popup":{"background":"#{{ $global_setting->cookie_consent_bg_color }}","text":"#{{ $global_setting->cookie_consent_text_color }}","border":"#b3d0e4"},"button":{"background":"#{{ $global_setting->cookie_consent_button_bg_color }}","text":"#{{ $global_setting->cookie_consent_button_text_color }}"}},"position":"bottom","padding":"large","margin":"none","content":{"message":"{{ $global_setting->cookie_consent_message }}","button":"{{ $global_setting->cookie_consent_button_text }}"}})});</script>
    @endif

    @if($global_setting->google_analytic_status == 'Show')
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ $global_setting->google_analytic_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', '{{ $global_setting->google_analytic_id }}');
    </script>
    @endif

    <style >
    :root {
        --theme-color1: #{{ $global_setting->theme_color }};
    }
    </style>

</head>

<body>

    <div class="page-wrapper">
        
        @if($global_setting->preloader == 'Show')
        <div class="preloader"></div>
        @endif

        @php
        $menu_arr = [];
        foreach($global_menu as $item) {
            $menu_arr[$item->name] = $item->status;
        }
        @endphp

        <!-- Old Header - Commented for TBL Tech Design -->
        {{-- <header class="main-header header-style-two">

            @include('front.layouts.top')

            <div class="header-lower">
                <!-- Main box -->
                <div class="main-box">
                    <div class="logo-box">
                        <div class="logo"><a href="{{ route('home') }}"><img src="{{ asset('uploads/'.$global_setting->logo) }}" alt="{{ env('APP_NAME') }}"></a></div>
                    </div>

                    <!--Nav Box-->
                    <div class="nav-outer">
                        <nav class="nav main-menu">
                            <ul class="navigation"> --}}

        <!-- New TBL Tech Header -->
        <nav class="bg-white border-b-[1px] border-[#d9d9d9] py-2">
            <div class="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
                <div class="relative flex h-16 items-center justify-between">
                    <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
                        <!-- Mobile menu button-->
                        <button type="button" id="mobile-menu-button"
                            class="relative inline-flex items-center justify-center rounded-md p-2 text-purple-700 hover:bg-purple-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                            aria-controls="mobile-menu" aria-expanded="false">
                            <span class="absolute -inset-0.5"></span>
                            <span class="sr-only">Open main menu</span>
                            <!-- Icon when menu is closed -->
                            <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                            </svg>
                            <!-- Icon when menu is open -->
                            <svg class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                        <div class="flex flex-shrink-0 items-center">
                            <a href="{{ route('home') }}"><img class="h-16 w-auto" src="{{ asset('uploads/'.$global_setting->logo) }}" alt="{{ env('APP_NAME') }}"></a>
                        </div>
                        <div class="hidden sm:ml-6 sm:block pr-16">
                            <div class="flex space-x-6 py-10">
                                <a href="#" class="hover:text-purple-700 hover:underline px-5 py-2 text-md font-bold" aria-current="page">منتجاتنا</a>

                                <div class="py-2 px-3 relative inline-block group">
                                    <button class="rounded-md hover:text-purple-700 hover:underline">
                                        خدماتنا
                                        <svg style="color: rgb(0, 0, 0);" class="w-5 h-5 inline-block" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M3.204 5h9.592L8 10.481 3.204 5zm-.753.659 4.796 5.48a1 1 0 0 0 1.506 0l4.796-5.48c.566-.647.106-1.659-.753-1.659H3.204a1 1 0 0 0-.753 1.659z" fill="#7e22ce"></path>
                                        </svg>
                                    </button>
                                    <ul class="absolute z-10 hidden group-hover:block py-1 bg-white border border-gray-200 rounded-md shadow-md w-40">
                                        @if($menu_arr['Services'] == 'Show')
                                        <li class="px-4 py-2">
                                            <a href="{{ route('services') }}" class="hover:text-blue-500 hover:underline">{{ __('Services') }}</a>
                                        </li>
                                        @endif
                                    </ul>
                                </div>

                                <a href="#" class="hover:text-purple-700 hover:underline px-3 py-2 text-md font-bold">من نحن</a>
                                <a href="#" class="hover:text-purple-700 hover:underline px-3 py-2 text-md font-bold">اعمالنا</a>
                                <a href="#" class="hover:text-purple-700 hover:underline px-3 py-2 text-md font-bold">عملائنا</a>
                                <a href="#" class="hover:text-purple-700 hover:underline px-3 py-2 text-md font-bold">فريقنا</a>
                                @if($menu_arr['Contact'] == 'Show')
                                <a href="{{ route('contact') }}" class="hover:text-purple-700 hover:underline px-3 py-2 text-md font-bold">تواصل معنا</a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile menu, show/hide based on menu state -->
            <div class="sm:hidden hidden" id="mobile-menu">
                <div class="space-y-1 px-2 pb-3 pt-2">
                    <a href="#" class="text-black hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 font-medium">الصفحة الرئيسية</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">منتجاتنا</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">خدماتنا</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">من نحن</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">اعمالنا</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">عملائنا</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">فريقنا</a>
                    <a href="#" class="text-black-300 hover:bg-purple-500 hover:text-white block rounded-md px-3 py-2 text-base font-medium">تواصل معنا</a>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function () {
                    var mobileMenuButton = document.getElementById('mobile-menu-button');
                    var mobileMenu = document.getElementById('mobile-menu');

                    mobileMenuButton.addEventListener('click', function () {
                        var expanded = this.getAttribute('aria-expanded') === 'true';

                        if (expanded) {
                            mobileMenu.classList.remove('block');
                            mobileMenu.classList.add('hidden');
                            this.setAttribute('aria-expanded', 'false');
                        } else {
                            mobileMenu.classList.remove('hidden');
                            mobileMenu.classList.add('block');
                            this.setAttribute('aria-expanded', 'true');
                        }
                    });
                });
            </script>
        </nav>



        {{-- Continue with old navigation code commented
                                <ul class="navigation">

                                @if($global_setting->home_show == 'All')
                                <li class="{{ (Request::is('/')||Route::is('home_1')||Route::is('home_2')||Route::is('home_3')||Route::is('home_4')) ? 'current' : ''; }} dropdown"><a href="javascript:;">{{ __('Home') }}</a>
                                    <ul >
                                        <li ><a href="{{ route('home_1') }}">{{ __('Home Layout 1') }}</a></li>
                                        <li ><a href="{{ route('home_2') }}">{{ __('Home Layout 2') }}</a></li>
                                        <li ><a href="{{ route('home_3') }}">{{ __('Home Layout 3') }}</a></li>
                                        <li ><a href="{{ route('home_4') }}">{{ __('Home Layout 4') }}</a></li>
                                    </ul>
                                </li>
                                @endif

                                @if($global_setting->home_show == 'Home 1')
                                <li class="{{ Route::is('home_1') ? 'current' : ''; }}">
                                    <a href="{{ route('home_1') }}">{{ __('Home') }}</a>
                                </li>
                                @endif

                                @if($global_setting->home_show == 'Home 2')
                                <li class="{{ Route::is('home_2') ? 'current' : ''; }}">
                                    <a href="{{ route('home_2') }}">{{ __('Home') }}</a>
                                </li>
                                @endif

                                @if($global_setting->home_show == 'Home 3')
                                <li class="{{ Route::is('home_3') ? 'current' : ''; }}">
                                    <a href="{{ route('home_3') }}">{{ __('Home') }}</a>
                                </li>
                                @endif

                                @if($global_setting->home_show == 'Home 4')
                                <li class="{{ Route::is('home_4') ? 'current' : ''; }}">
                                    <a href="{{ route('home_4') }}">{{ __('Home') }}</a>
                                </li>
                                @endif --}}


        @yield('content')

        <!-- Main Footer -->
        <footer class="main-footer">
            <div class="bg bg-pattern-6"></div>
            
            @if($global_setting->footer_phone != '' || $global_setting->footer_email != '' || $global_setting->footer_address != '')
            <div class="footer-upper">
                <div class="auto-container">
                    <div class="row">
                        @if($global_setting->footer_phone != '')
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-phone-square"></i>
                                <span class="sub-title">{{ __('Call Anytime') }}</span>
                                <div class="text"><a href="tel:+{{ $global_setting->footer_phone }}">{{ $global_setting->footer_phone }}</a></div>
                            </div>
                        </div>
                        @endif
                        
                        @if($global_setting->footer_email != '')
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-envelope"></i>
                                <span class="sub-title">{{ __('Send Email') }}</span>
                                <div class="text"><a href="mailto:{{ $global_setting->footer_email }}">{{ $global_setting->footer_email }}</a></div>
                            </div>
                        </div>
                        @endif

                        @if($global_setting->footer_address != '')
                        <div class="contact-info-block col-lg-4 col-md-6">
                            <div class="inner">
                                <i class="icon fa fa-map-marker"></i>
                                <span class="sub-title">{{ __('Address') }}</span>
                                <div class="text">{{ $global_setting->footer_address }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            <!-- Widgets Section -->
            <div class="widgets-section">
                <div class="auto-container">
                    <div class="row">
                        <!-- Footer COlumn -->
                        <div class="footer-column col-xl-5 col-lg-4 col-md-12">
                            <div class="footer-widget about-widget">
                                <div class="widget-content">
                                    <div class="logo"><a href="{{ route('home') }}"><img src="{{ asset('uploads/'.$global_setting->logo) }}" alt="{{ env('APP_NAME') }}"></a></div>
                                    <div class="text">{!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_setting->footer_text))) !!}</div>
                                    @if($global_setting->twitter!=''||$global_setting->facebook!=''||$global_setting->linkedin!=''||$global_setting->instagram!=''||$global_setting->youtube!=''||$global_setting->pinterest)
                                    <ul class="social-icon-two">
                                        @if($global_setting->twitter!='')
                                        <li ><a href="{{ $global_setting->twitter }}"><i class="fab fa-twitter"></i></a></li>
                                        @endif

                                        @if($global_setting->facebook!='')
                                        <li ><a href="{{ $global_setting->facebook }}"><i class="fab fa-facebook-f"></i></a></li>
                                        @endif

                                        @if($global_setting->linkedin!='')
                                        <li ><a href="{{ $global_setting->linkedin }}"><i class="fab fa-linkedin-in"></i></a></li>
                                        @endif

                                        @if($global_setting->instagram!='')
                                        <li ><a href="{{ $global_setting->instagram }}"><i class="fab fa-instagram"></i></a></li>
                                        @endif

                                        @if($global_setting->youtube!='')
                                        <li ><a href="{{ $global_setting->youtube }}"><i class="fab fa-youtube"></i></a></li>
                                        @endif

                                        @if($global_setting->pinterest!='')
                                        <li ><a href="{{ $global_setting->pinterest }}"><i class="fab fa-pinterest-p"></i></a></li>
                                        @endif
                                    </ul>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <!-- Footer COlumn -->
                        <div class="footer-column col-xl-4 col-lg-4 col-md-6">
                            <div class="widget links-widget">
                                <h5 class="widget-title">{{ $global_setting->footer_links_heading }}</h5>
                                <div class="widget-content">
                                    <ul class="user-links two-column">
                                        <li ><a href="{{ route('about') }}">{{ __('About') }}</a></li>
                                        <li ><a href="{{ route('team_members') }}">{{ __('Team Members') }}</a></li>
                                        <li ><a href="{{ route('services') }}">{{ __('Services') }}</a></li>
                                        <li ><a href="{{ route('testimonials') }}">{{ __('Testimonials') }}</a></li>
                                        <li ><a href="{{ route('portfolios') }}">{{ __('Portfolios') }}</a></li>
                                        <li ><a href="{{ route('faqs') }}">{{ __('FAQ') }}</a></li>
                                        <li ><a href="{{ route('contact') }}">{{ __('Contact') }}</a></li>
                                        <li ><a href="{{ route('terms') }}">{{ __('Terms of Use') }}</a></li>
                                        <li ><a href="{{ route('pricing_plans') }}">{{ __('Pricing') }}</a></li>
                                        <li ><a href="{{ route('privacy') }}">{{ __('Privacy Policy') }}</a></li>
                                    </ul>                                
                                </div>
                            </div>
                        </div>
                        <div class="footer-column col-xl-3 col-lg-4 col-md-6 col-sm-12">
                            <div class="widget newsletter-widget">
                                <h5 class="widget-title">{{ $global_setting->footer_subscriber_heading }}</h5>
                                <div class="widget-content">
                                    <div class="text">{!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($global_setting->footer_subscriber_text))) !!}</div>
                                    <div class="subscribe-form">
                                        <form method="post" action="{{ route('subscriber_submit') }}">
                                            @csrf
                                            <div class="form-group">
                                                <input type="email" name="email" class="email" value="" placeholder="{{ __('Email Address') }}" required>
                                            </div>
                                            <div class="form-group">
                                                <button type="submit" class="theme-btn btn-style-one hover-light"><span class="btn-title">{{ __('Subscribe') }}</span></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="auto-container">
                    <div class="copyright-text">{{ $global_setting->footer_copyright }}</div>
                </div>
            </div>
        </footer>

    </div>
    
    <div class="scroll-to-top scroll-to-target" data-target="html"><span class="fa fa-angle-up"></span></div>
    
    @include('front.layouts.scripts')

    @if($global_setting->tawk_live_chat_status == 'Show')
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/{{ $global_setting->tawk_live_chat_property_id }}/default';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
    @endif

</body>
</html>