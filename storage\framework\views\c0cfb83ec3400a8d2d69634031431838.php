<!DOCTYPE html>
<html <?php if(session('sess_lang_direction') == 'Right to Left (RTL)'): ?> dir="rtl" <?php endif; ?> lang="<?php echo e(session('sess_lang_code')); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/png" href="uploads/favicon.png">
    
    <title><?php echo e(__('Admin Panel')); ?></title>

    <link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <?php echo $__env->make('admin.layouts.styles', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin.layouts.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body id="page-top">
    <div id="wrapper">
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="<?php echo e(route('admin_dashboard')); ?>">
                <div class="sidebar-brand-text mx-3 ttn">
                    <div class="right">
                        <?php echo e(env('APP_NAME')); ?>

                    </div>
                </div>
            </a>

            <hr class="sidebar-divider my-0">

            <?php echo $__env->make('admin.layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <hr class="sidebar-divider">

            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>

        <div id="content-wrapper" class="d-flex flex-column pb_50">
            <div id="content">

                <?php echo $__env->make('admin.layouts.nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div class="container-fluid">

                    <?php echo $__env->yieldContent('content'); ?>

                </div>

            </div>
        </div>
    </div>

    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <?php echo $__env->make('admin.layouts.scripts_footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    
</body>
</html>
<?php /**PATH /home/<USER>/cms.pronixs.com/resources/views/admin/layouts/master.blade.php ENDPATH**/ ?>