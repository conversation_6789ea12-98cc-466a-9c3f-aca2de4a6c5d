@extends('front.layouts.master')

@section('seo_title', $global_setting->home_seo_title)
@section('seo_meta_description', $global_setting->home_seo_meta_description)

@section('content')

<!-- Hero Section -->
<section class="min-h-screen bg-gray-100" id="aboutUs">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7" dir="rtl">
            @if($sliders->count() > 0)
                @php $slider = $sliders->first(); @endphp
                <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl text-right">
                    @if($slider->text != '')
                        {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($slider->text))) !!}
                    @else
                        TBL TECH شركة متخصصة في
                        <div class="relative inline-flex">
                            <span class="absolute inset-x-0 bottom-0 border-b-[30px] border-purple-500"></span>
                            <span class="relative text-4xl font-bold text-black sm:text-6xl lg:text-7xl">
                                هندسة البرمجيات.
                            </span>
                        </div>
                    @endif
                </h1>
            @else
                <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl text-right">
                    TBL TECH شركة متخصصة في
                    <div class="relative inline-flex">
                        <span class="absolute inset-x-0 bottom-0 border-b-[30px] border-purple-500"></span>
                        <span class="relative text-4xl font-bold text-black sm:text-6xl lg:text-7xl">
                            هندسة البرمجيات.
                        </span>
                    </div>
                </h1>
            @endif
            
            @if($welcome_one_items && $welcome_one_items->text)
                <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                    {!! str_replace(["<p>", "</p>"], ["", ""], clean(nl2br($welcome_one_items->text))) !!}
                </p>
            @else
                <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                    نقدم حلولًا مبتكرة في تطوير الأنظمة وتصميم وبرمجة المواقع الإلكترونية والتطبيقات والمتاجر الإلكترونية.
                </p>
                <p class="max-w-2xl mb-6 font-light text-gray-500 lg:mb-8 md:text-lg lg:text-xl dark:text-gray-500">
                    نلتزم دائمآ بتقديم خدمات عالية الجودة تلبي احتياجات العملاء، مع التركيز على تقديم أفضل أداء، و أعلى مستوى من الأمان والحماية ، ونعمل دومآ على تحسين تجربة المستخدم لتوفير حلول تقنية تدعم نمو أعمالهم وتحقق أهدافهم الرقمية.
                </p>
            @endif

            @if($sliders->count() > 0 && $sliders->first()->button_text != '')
                <a href="{{ $sliders->first()->button_url }}" 
                   class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    {{ $sliders->first()->button_text }}
                </a>
            @else
                <a href="#contact" 
                   class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-center text-white rounded-md bg-purple-700 hover:bg-purple-400 focus:ring-4 focus:ring-gray-100">
                    اطلب موقعك الان
                </a>
            @endif
        </div>
        
        <div class="lg:mt-0 mt-10 lg:col-span-5 lg:flex">
            @if($sliders->count() > 0 && $sliders->first()->photo)
                <img src="{{ asset('uploads/'.$sliders->first()->photo) }}" alt="Hero Image" class="w-full h-auto" />
            @else
                <img src="https://cdn.rareblocks.xyz/collection/celebration/images/hero/1/hero-img.png" alt="Hero Image" class="w-full h-auto" />
            @endif
        </div>
    </div>
</section>
<!-- End Hero Section -->

@endsection
