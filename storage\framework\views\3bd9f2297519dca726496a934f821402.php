<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Edit Slider')); ?></h1>
    <a href="<?php echo e(route('admin_slider_index')); ?>" class="d-none d-sm-inline-block btn btn-primary shadow-sm"><i class="fas fa-bars"></i> <?php echo e(__('All Items')); ?>

    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('admin_slider_update',$slider->id)); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                        <div><img src="<?php echo e(asset('uploads/'.$slider->photo)); ?>" alt="" class="w_200"></div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                        <div><input type="file" name="photo"></div>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Text')); ?></label>
                        <textarea name="text" class="form-control h_100" cols="30" rows="10"><?php echo e($slider->text); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Button Text')); ?></label>
                        <input type="text" name="button_text" class="form-control" value="<?php echo e($slider->button_text); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Button URL')); ?></label>
                        <input type="text" name="button_url" class="form-control" value="<?php echo e($slider->button_url); ?>">
                    </div>
                    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/cms.tblyemen.store/resources/views/admin/slider/edit.blade.php ENDPATH**/ ?>