<?php $__env->startSection('content'); ?>
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"><?php echo e(__('Why Choose Two - Items')); ?></h1>
</div>


<div class="row">
    <div class="col-md-7">
        <div class="card shadow mb-4">
            <div class="card-body">
                <form action="<?php echo e(route('admin_why_choose_two_item_update')); ?>" method="post" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Heading')); ?></label>
                        <input type="text" name="heading" class="form-control" value="<?php echo e($why_choose_two_items->heading); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Subheading')); ?></label>
                        <input type="text" name="subheading" class="form-control" value="<?php echo e($why_choose_two_items->subheading); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Photo Over Text')); ?></label>
                        <textarea name="photo_over_text" class="form-control h_100" cols="30" rows="10"><?php echo e($why_choose_two_items->photo_over_text); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="" class="form-label"><?php echo e(__('Photo Over heading')); ?></label>
                        <input type="text" name="photo_over_heading" class="form-control" value="<?php echo e($why_choose_two_items->photo_over_heading); ?>">
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Existing Photo')); ?></label>
                                <div class="photo-container">
                                    <a href="<?php echo e(asset('uploads/'.$why_choose_two_items->photo)); ?>" class="magnific"><img src="<?php echo e(asset('uploads/'.$why_choose_two_items->photo)); ?>" alt=""></a>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="" class="form-label"><?php echo e(__('Change Photo')); ?></label>
                                <div><input type="file" name="photo"></div>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success mb-50 btn-common"><?php echo e(__('Update')); ?></button>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-5">
        <div class="card shadow mb-4">
            <div class="card-body">
                <h4><b><?php echo e(__('Elements')); ?></b></h4>
                <div class="mb_10">
                    <a href="" data-bs-toggle="modal" data-bs-target="#add_modal"><i class="fas fa-plus"></i> <?php echo e(__('Add Item')); ?></a>
                </div>

                <!-- Modal -->
                <div class="modal fade" id="add_modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h1 class="modal-title fs-5" id="exampleModalLabel"><?php echo e(__('Add Element')); ?></h1>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="<?php echo e(route('admin_why_choose_two_item_element_submit')); ?>" method="post">
                                    <?php echo csrf_field(); ?>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Heading')); ?>*</label>
                                        <textarea name="heading" class="form-control h_100" cols="30" rows="10" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label"><?php echo e(__('Icon')); ?>*</label>
                                        <select id="iconSelect" name="icon" class="form-select">
                                            <option value=""><?php echo e(__('Select Icon')); ?></option>
                                            <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($icon->icon_code); ?>"><?php echo e($icon->icon_code); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <div id="iconPreview"></i></div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <button type="submit" class="btn btn-primary btn-sm"><?php echo e(__('Submit')); ?></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- // Modal -->


                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead>
                            <tr>
                                <th><?php echo e(__('Icon')); ?></th>
                                <th><?php echo e(__('Heading')); ?></th>
                                <th><?php echo e(__('Action')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $why_choose_two_item_elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <i class="<?php echo e($item->icon); ?> fz_40"></i>
                                </td>
                                <td><?php echo e($item->heading); ?></td>
                                <td>
                                    <a href="" class="btn btn-primary btn-sm btn-block" data-bs-toggle="modal" data-bs-target="#edit_modal_<?php echo e($loop->iteration); ?>"><i class="fas fa-edit"></i></a>
                                    <a href="<?php echo e(route('admin_why_choose_two_item_element_delete',$item->id)); ?>" class="btn btn-danger btn-sm btn-block" onClick="return confirm('<?php echo e(__('Are you sure?')); ?>')"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>

                            <!-- Modal -->
                            <div class="modal fade" id="edit_modal_<?php echo e($loop->iteration); ?>" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h1 class="modal-title fs-5" id="exampleModalLabel"><?php echo e(__('Edit Element')); ?></h1>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <form action="<?php echo e(route('admin_why_choose_two_item_element_update',$item->id)); ?>" method="post">
                                                <?php echo csrf_field(); ?>
                                                <div class="mb-3">
                                                    <label for="" class="form-label"><?php echo e(__('Heading')); ?>*</label>
                                                    <textarea name="heading" class="form-control h_100" cols="30" rows="10" required><?php echo e($item->heading); ?></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="" class="form-label"><?php echo e(__('Icon')); ?>*</label>
                                                    <select id="iconSelectEdit" name="icon" class="form-select">
                                                        <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($icon->icon_code); ?>" <?php if($icon->icon_code==$item->icon): ?> selected <?php endif; ?>><?php echo e($icon->icon_code); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div id="iconPreviewEdit"><i class="icon <?php echo e($item->icon); ?>"></i></div>
                                                </div>
                                                <div class="mb-3">
                                                    <button type="submit" class="btn btn-primary btn-sm"><?php echo e(__('Update')); ?></button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- // Modal -->
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
       
<script>
    window.onload = function () {
        document.getElementById('iconSelect').addEventListener('change', function () {
            var selectedValue = this.value;
            var previewElement = document.getElementById('iconPreview');
            previewElement.innerHTML = '<i class="icon ' + selectedValue + '"></i>';
        });
        document.getElementById('iconSelectEdit').addEventListener('change', function () {
            var selectedValueEdit = this.value;
            var previewElementEdit = document.getElementById('iconPreviewEdit');
            previewElementEdit.innerHTML = '<i class="icon ' + selectedValueEdit + '"></i>';
        });
    };
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/why_choose/two.blade.php ENDPATH**/ ?>